2025-06-29 12:53:27 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 12:53:28 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 12:53:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:53:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:54:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:54:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 13:09:17 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:09:17 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:09:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:09:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:11:16 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:13:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:15:48 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:28:31 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:28:31 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:48:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:48:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:49:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:49:43 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 13:49:43 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 13:49:43 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 13:50:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 13:50:04 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 13:50:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 13:50:22 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 13:50:22 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 13:50:22 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 14:04:39 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:04:39 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:12:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:12:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:12:58 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:13:24 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 14:13:24 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:13:24 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 14:14:31 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 14:14:31 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:14:31 | ERROR | QuotaCreationTool.AIModelProcessor | ❌ 提取的JSON仍然无效
2025-06-29 14:14:31 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无法修复模型 google/gemma-3-27b 的JSON格式，返回原始内容
2025-06-29 14:14:31 | WARNING | QuotaCreationTool.AIModelProcessor |    原始内容预览: ```json
{
  "quotas": [
    {
      "parent_quota": {
        "code": "04-01-1-1",
        "name": "综合用工三类",
        "work_content": null,
        "unit": "工日"
      },
      "resource_consumption": [...
2025-06-29 14:15:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 14:15:04 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:15:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 14:16:41 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:16:47 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:16:47 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:16:47 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:16:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:16:54 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:16:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:16:56 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:16:56 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:16:56 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:18:10 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:25:27 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:25:27 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:26:20 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:26:20 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:27:37 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:33:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:33:31 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:33:31 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:33:31 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:33:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:33:35 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:33:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:33:37 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:33:37 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:33:37 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:44:02 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:44:02 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:45:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:45:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:45:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:45:52 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen/qwen2.5-vl-7b 返回有效JSON格式
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen/qwen2.5-vl-7b 数据验证完成，修复了 4 个定额项
2025-06-29 15:01:54 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 15:01:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 15:01:54 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:01:54 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 15:01:55 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 15:01:55 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:07:50 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:07:50 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
