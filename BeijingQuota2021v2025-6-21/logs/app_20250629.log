2025-06-29 12:53:27 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 12:53:28 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:53:28 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 12:53:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:53:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:54:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 12:54:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 2 个模型
2025-06-29 13:09:17 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:09:17 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:09:17 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:09:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:09:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:11:16 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:13:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:15:48 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:28:31 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:28:31 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:28:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 13:48:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:48:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:49:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 13:49:43 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 13:49:43 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 13:49:43 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 13:50:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 13:50:04 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 13:50:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 13:50:22 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 13:50:22 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 13:50:22 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 14:04:39 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:04:39 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:04:39 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:12:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:12:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:12:58 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:13:24 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 14:13:24 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:13:24 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 14:14:31 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 14:14:31 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:14:31 | ERROR | QuotaCreationTool.AIModelProcessor | ❌ 提取的JSON仍然无效
2025-06-29 14:14:31 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无法修复模型 google/gemma-3-27b 的JSON格式，返回原始内容
2025-06-29 14:14:31 | WARNING | QuotaCreationTool.AIModelProcessor |    原始内容预览: ```json
{
  "quotas": [
    {
      "parent_quota": {
        "code": "04-01-1-1",
        "name": "综合用工三类",
        "work_content": null,
        "unit": "工日"
      },
      "resource_consumption": [...
2025-06-29 14:15:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 14:15:04 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:15:04 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 google/gemma-3-27b 响应中提取有效JSON
2025-06-29 14:16:41 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:16:47 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:16:47 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:16:47 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:16:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:16:54 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:16:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:16:56 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:16:56 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:16:56 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:18:10 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:25:27 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:25:27 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:25:27 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:26:20 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:26:20 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:27:37 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:33:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:33:31 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:33:31 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:33:31 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:33:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:33:35 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:33:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:33:37 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:33:37 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:33:37 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:44:02 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:44:02 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:44:02 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 14:45:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:45:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:45:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 14:45:52 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从模型 qwen/qwen2.5-vl-7b 响应中提取有效JSON
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen/qwen2.5-vl-7b 返回有效JSON格式
2025-06-29 14:45:52 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen/qwen2.5-vl-7b 数据验证完成，修复了 4 个定额项
2025-06-29 15:01:54 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 15:01:54 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 15:01:54 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:01:54 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 15:01:55 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 3 个模型
2025-06-29 15:01:55 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:07:31 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:07:50 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:07:50 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:09:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:11:02 | ERROR | QuotaCreationTool.AIModelProcessor | ❌ LM Studio模型 qwen2.5-vl-32b-instruct 处理异常: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)
2025-06-29 15:11:02 | ERROR | QuotaCreationTool.AIModelProcessor |    异常详情: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 1430, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 720, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\dev\BeijingQuota2021v2025-6-21\src\ai_model_processor.py", line 305, in process_image_with_lm_studio
    response = requests.post(
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)

2025-06-29 15:25:29 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 15:25:29 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:25:29 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:25:29 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 15:25:30 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:25:30 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 15:26:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:26:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:26:38 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:26:38 | INFO | QuotaCreationTool.AIModelProcessor | 🕒 检测到大参数模型 qwen2.5-vl-32b-instruct，设置超时时间为 900 秒
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen2.5-vl-32b-instruct 响应成功
2025-06-29 15:31:35 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen2.5-vl-32b-instruct 返回的JSON格式有问题，尝试修复...
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 qwen2.5-vl-32b-instruct 包含推理过程，提取JSON部分
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen2.5-vl-32b-instruct 返回有效JSON格式
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 0 包含 1 个有效资源
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 1 包含 2 个有效资源
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 2 包含 2 个有效资源
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 3 包含 2 个有效资源
2025-06-29 15:31:35 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen2.5-vl-32b-instruct 数据验证完成，修复了 4 个定额项
2025-06-29 15:33:57 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:35:58 | ERROR | QuotaCreationTool.AIModelProcessor | ❌ LM Studio模型 google/gemma-3-27b 处理异常: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)
2025-06-29 15:35:58 | ERROR | QuotaCreationTool.AIModelProcessor |    异常详情: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 1430, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 720, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\dev\BeijingQuota2021v2025-6-21\src\ai_model_processor.py", line 313, in process_image_with_lm_studio
    response = requests.post(
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)

2025-06-29 15:36:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 15:38:49 | ERROR | QuotaCreationTool.AIModelProcessor | ❌ LM Studio模型 google/gemma-3-27b 处理异常: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)
2025-06-29 15:38:49 | ERROR | QuotaCreationTool.AIModelProcessor |    异常详情: Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 1430, in getresponse
    response.begin()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 720, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\dev\BeijingQuota2021v2025-6-21\src\ai_model_processor.py", line 313, in process_image_with_lm_studio
    response = requests.post(
               ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=120)

2025-06-29 15:41:38 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:41:38 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:42:07 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 google/gemma-3-27b 响应成功
2025-06-29 15:42:46 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 google/gemma-3-27b 返回的JSON格式有问题，尝试修复...
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 google/gemma-3-27b 包含推理过程，提取JSON部分
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 google/gemma-3-27b 返回有效JSON格式
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔄 跳过空值资源: 反铲挖掘机
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 0 包含 5 个有效资源
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔄 跳过空值资源: 空压机 6m³/min
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔄 跳过空值资源: 风镐
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 1 包含 4 个有效资源
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔄 跳过空值资源: 空压机 6m³/min
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔄 跳过空值资源: 风镐
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 2 包含 4 个有效资源
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔄 跳过空值资源: 空压机 6m³/min
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | 🔄 跳过空值资源: 风镐
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 3 包含 4 个有效资源
2025-06-29 15:42:46 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 google/gemma-3-27b 数据验证完成，修复了 4 个定额项
2025-06-29 15:56:29 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 15:56:33 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 nanonets-ocr-s 响应成功
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 nanonets-ocr-s 返回的JSON格式有问题，尝试修复...
2025-06-29 15:56:33 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 nanonets-ocr-s 包含推理过程，提取JSON部分
2025-06-29 15:56:33 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 15:56:33 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 15:56:33 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 nanonets-ocr-s 返回有效JSON格式
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无效资源消耗数据: {'resource_code': '资源编号', 'category': '', 'name': '子项名称', 'unit': '', 'consumption': '消耗量'}
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 定额项 0 没有有效资源
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无效资源消耗数据: {'resource_code': '资源编号', 'category': '', 'name': '子项名称', 'unit': '', 'consumption': '消耗量'}
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 定额项 1 没有有效资源
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无效资源消耗数据: {'resource_code': '资源编号', 'category': '', 'name': '子项名称', 'unit': '', 'consumption': '消耗量'}
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 定额项 2 没有有效资源
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无效资源消耗数据: {'resource_code': '资源编号', 'category': '', 'name': '子项名称', 'unit': '', 'consumption': '消耗量'}
2025-06-29 15:56:33 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 定额项 3 没有有效资源
2025-06-29 15:56:33 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 nanonets-ocr-s 数据验证完成，修复了 0 个定额项
2025-06-29 15:59:59 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 4 个模型
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen/qwen2.5-vl-7b 响应成功
2025-06-29 16:00:05 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen/qwen2.5-vl-7b 返回的JSON格式有问题，尝试修复...
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 qwen/qwen2.5-vl-7b 包含推理过程，提取JSON部分
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen/qwen2.5-vl-7b 返回有效JSON格式
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 0 包含 1 个有效资源
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 1 包含 2 个有效资源
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 2 包含 2 个有效资源
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 3 包含 2 个有效资源
2025-06-29 16:00:05 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 qwen/qwen2.5-vl-7b 数据验证完成，修复了 4 个定额项
2025-06-29 18:11:09 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:11:09 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:12:03 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 unsloth/qwen2.5-vl-7b-instruct 响应成功
2025-06-29 18:12:13 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 unsloth/qwen2.5-vl-7b-instruct 返回的JSON格式有问题，尝试修复...
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 unsloth/qwen2.5-vl-7b-instruct 包含推理过程，提取JSON部分
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 返回有效JSON格式
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 0 包含 1 个有效资源
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 1 包含 2 个有效资源
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 2 包含 2 个有效资源
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 3 包含 2 个有效资源
2025-06-29 18:12:13 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 数据验证完成，修复了 4 个定额项
2025-06-29 18:13:44 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 unsloth/qwen2.5-vl-7b-instruct 响应成功
2025-06-29 18:13:54 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 unsloth/qwen2.5-vl-7b-instruct 返回的JSON格式有问题，尝试修复...
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 unsloth/qwen2.5-vl-7b-instruct 包含推理过程，提取JSON部分
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 返回有效JSON格式
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 0 包含 1 个有效资源
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 1 包含 2 个有效资源
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 2 包含 2 个有效资源
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 3 包含 2 个有效资源
2025-06-29 18:13:54 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 数据验证完成，修复了 4 个定额项
2025-06-29 18:14:29 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 unsloth/qwen2.5-vl-7b-instruct 响应成功
2025-06-29 18:14:39 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 unsloth/qwen2.5-vl-7b-instruct 返回的JSON格式有问题，尝试修复...
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 unsloth/qwen2.5-vl-7b-instruct 包含推理过程，提取JSON部分
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 返回有效JSON格式
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 0 包含 1 个有效资源
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 1 包含 2 个有效资源
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 2 包含 2 个有效资源
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 3 包含 2 个有效资源
2025-06-29 18:14:39 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 数据验证完成，修复了 4 个定额项
2025-06-29 18:36:58 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 18:36:58 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:36:58 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 18:36:58 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 18:36:58 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:36:58 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 18:38:42 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 18:38:42 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:01:24 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 19:01:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:01:25 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:01:25 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 19:01:25 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:01:25 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:31:10 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 19:31:10 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:31:10 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:31:10 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 19:31:10 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:31:10 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:32:42 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 19:32:42 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:32:42 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:32:42 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 19:32:42 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:32:42 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:33:23 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:33:23 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:33:39 | INFO | QuotaCreationTool.MainApp | 📄 定额PDF文件已保存: 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf -> quota_20250629_193339_13433ac3
2025-06-29 19:33:39 | INFO | QuotaCreationTool.MainApp |    存储路径: stored_pdfs\quota_pdfs\quota_20250629_193339_13433ac3.pdf
2025-06-29 19:33:39 | INFO | QuotaCreationTool.MainApp |    文件大小: 1.64 MB
2025-06-29 19:34:38 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:34:38 | INFO | QuotaCreationTool.AIModelProcessor | 🕒 设置模型 unsloth/qwen2.5-vl-7b-instruct 超时时间为 900 秒（15分钟）
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 unsloth/qwen2.5-vl-7b-instruct 响应成功
2025-06-29 19:34:48 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 unsloth/qwen2.5-vl-7b-instruct 返回的JSON格式有问题，尝试修复...
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | 🔍 检测到模型 unsloth/qwen2.5-vl-7b-instruct 包含推理过程，提取JSON部分
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 从```json代码块中提取JSON
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 成功从推理过程中提取有效JSON
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 返回有效JSON格式
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 0 包含 1 个有效资源
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 1 包含 2 个有效资源
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 2 包含 2 个有效资源
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 定额项 3 包含 2 个有效资源
2025-06-29 19:34:48 | INFO | QuotaCreationTool.AIModelProcessor | ✅ 模型 unsloth/qwen2.5-vl-7b-instruct 数据验证完成，修复了 4 个定额项
2025-06-29 19:41:09 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 19:41:09 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:41:09 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:41:09 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 19:41:09 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:41:09 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:42:15 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 19:42:15 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:42:15 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:42:15 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 19:42:15 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:42:15 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:44:16 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 19:44:16 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:44:16 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:44:16 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 19:44:16 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:44:16 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:44:17 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-29 19:46:02 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-29 19:46:02 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-29 19:46:02 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-29 19:46:02 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-29 19:46:02 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-29 19:46:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:46:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:46:14 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-29 19:46:14 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-29 19:46:14 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-29 19:46:14 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-29 19:46:16 | INFO | QuotaCreationTool.MainApp | 📄 已加载存储的定额PDF: 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf
2025-06-29 19:56:12 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 19:56:12 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:56:12 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:56:12 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 19:56:12 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 19:56:12 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 19:56:12 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-29 20:27:47 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-29 20:27:47 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-29 20:27:47 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-29 20:27:47 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-29 20:27:47 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-29 20:27:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 20:27:47 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 5 个模型
2025-06-29 20:49:27 | INFO | QuotaCreationTool.MainApp | ✅ API密钥已保存到持久化配置
2025-06-29 21:02:12 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-29 21:02:12 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-29 21:02:12 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-29 21:02:12 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-29 21:02:12 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-29 21:02:12 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 21:02:12 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 21:03:23 | INFO | QuotaCreationTool.MainApp | 📄 已加载存储的定额PDF: 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf
2025-06-29 21:04:08 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 21:04:08 | INFO | QuotaCreationTool.AIModelProcessor | 🕒 设置模型 qwen2.5-vl-72b-instruct 超时时间为 900 秒（15分钟）
2025-06-29 21:16:53 | INFO | QuotaCreationTool.AIModelProcessor | ✅ LM Studio模型 qwen2.5-vl-72b-instruct 响应成功
2025-06-29 21:16:53 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen2.5-vl-72b-instruct 返回的JSON格式有问题，尝试修复...
2025-06-29 21:16:53 | ERROR | QuotaCreationTool.AIModelProcessor | ❌ 提取的JSON仍然无效
2025-06-29 21:16:53 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无法修复模型 qwen2.5-vl-72b-instruct 的JSON格式，返回原始内容
2025-06-29 21:16:53 | WARNING | QuotaCreationTool.AIModelProcessor |    原始内容预览: {
  "quotas": [
    {
      "parent_quota": {
        "code": "04-01-1-29",
        "name": "人工回填土 2:8",
        "work_content": "拌合、回填、平整、夯实等。",
        "unit": "m³"
      },
      "resource_consumpt...
2025-06-29 21:16:53 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen2.5-vl-72b-instruct 返回的JSON格式有问题，尝试修复...
2025-06-29 21:16:53 | ERROR | QuotaCreationTool.AIModelProcessor | ❌ 提取的JSON仍然无效
2025-06-29 21:16:53 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 无法修复模型 qwen2.5-vl-72b-instruct 的JSON格式，返回原始内容
2025-06-29 21:16:53 | WARNING | QuotaCreationTool.AIModelProcessor |    原始内容预览: {
  "quotas": [
    {
      "parent_quota": {
        "code": "04-01-1-29",
        "name": "人工回填土 2:8",
        "work_content": "拌合、回填、平整、夯实等。",
        "unit": "m³"
      },
      "resource_consumpt...
2025-06-29 21:16:53 | WARNING | QuotaCreationTool.AIModelProcessor | ⚠️ 模型 qwen2.5-vl-72b-instruct JSON解析失败，返回原始内容
2025-06-29 21:22:55 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:10:40 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 22:10:40 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:10:40 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:10:40 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 22:10:40 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:10:40 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:10:40 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-29 22:11:53 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 22:11:53 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:11:53 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:11:53 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 22:11:53 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:11:53 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:11:53 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-29 22:13:56 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-29 22:13:56 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-29 22:13:56 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-29 22:13:56 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-29 22:13:56 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-29 22:13:56 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:13:56 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:28:43 | INFO | QuotaCreationTool.MainApp | ✅ 定额数据库配置已保存到持久化存储
2025-06-29 22:28:43 | INFO | QuotaCreationTool.MainApp | 数据库创建策略: 智能合并现有数据
2025-06-29 22:28:43 | INFO | QuotaCreationTool.MainApp | 数据库已存在，将智能合并新数据: POSTGRESQL数据库: quota_database.db (可能存在)
2025-06-29 22:32:34 | INFO | QuotaCreationTool.MainApp | ✅ 定额数据库配置已保存到持久化存储
2025-06-29 22:32:34 | INFO | QuotaCreationTool.MainApp | 数据库创建策略: 智能合并现有数据
2025-06-29 22:32:34 | INFO | QuotaCreationTool.MainApp | 数据库已存在，将智能合并新数据: POSTGRESQL数据库: beijing2021_quota_database.db (可能存在)
2025-06-29 22:42:34 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 22:42:35 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:42:35 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:42:35 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 22:42:35 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:42:35 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:42:35 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-29 22:43:05 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 22:43:05 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:43:05 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:43:05 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 22:43:05 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:43:05 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 22:43:05 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-29 22:43:48 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-29 22:43:48 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-29 22:43:48 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-29 22:43:48 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-29 22:43:48 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-29 22:43:48 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:43:48 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:57:21 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-29 22:57:21 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-29 22:57:21 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-29 22:57:21 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-29 22:57:21 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-29 22:57:21 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:57:21 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 22:58:38 | INFO | QuotaCreationTool.MainApp | ✅ 定额数据库配置已保存到持久化存储
2025-06-29 22:58:38 | INFO | QuotaCreationTool.MainApp | 数据库创建策略: 智能合并现有数据
2025-06-29 22:58:38 | INFO | QuotaCreationTool.MainApp | 数据库已存在，将智能合并新数据: POSTGRESQL数据库: beijing2021_quota_database (可能存在)
2025-06-29 23:05:42 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-29 23:05:42 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 23:05:42 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 23:05:42 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-29 23:05:42 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-29 23:05:42 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-29 23:05:42 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
