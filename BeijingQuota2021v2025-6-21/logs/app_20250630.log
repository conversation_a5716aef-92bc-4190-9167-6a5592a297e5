2025-06-30 00:02:01 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:02:01 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:02:01 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:02:01 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:02:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:02:02 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:02:02 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:02:02 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:03:20 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:03:20 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:03:20 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:03:20 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:03:20 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:03:20 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:03:20 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-30 00:03:44 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:03:44 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:03:44 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:03:44 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:03:45 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:03:45 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:03:45 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:03:45 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:04:48 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:04:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:04:49 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:04:49 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:04:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:04:49 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:04:49 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:04:49 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:05:16 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:05:16 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:05:16 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:05:16 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:05:17 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:05:17 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:05:17 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-30 00:06:10 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-30 00:06:10 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-30 00:06:10 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-30 00:06:10 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-30 00:06:10 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-30 00:06:11 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:06:11 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:11:17 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:11:17 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:11:17 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:11:17 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:11:17 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:11:17 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:12:32 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:12:32 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:12:32 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:12:32 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:12:32 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:12:32 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:15:37 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:15:37 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:15:37 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:15:37 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:15:37 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:15:37 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:16:14 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:16:14 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:16:14 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:16:14 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:16:14 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:16:14 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:17:28 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:17:28 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:17:28 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:17:28 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:17:28 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:17:28 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:17:29 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
2025-06-30 00:20:17 | INFO | QuotaCreationTool.MainApp | 🧪 测试界面加载事件被触发
2025-06-30 00:20:17 | INFO | QuotaCreationTool.MainApp | 🔄 开始获取已存储的定额PDF列表...
2025-06-30 00:20:17 | INFO | QuotaCreationTool.MainApp | 📋 找到 1 个已存储的定额PDF
2025-06-30 00:20:17 | INFO | QuotaCreationTool.MainApp |    - 04 市政工程预算消耗量标准  01通用02道路03桥涵工程.pdf (1.64 MB)
2025-06-30 00:20:17 | INFO | QuotaCreationTool.MainApp | ✅ 成功生成 1 个选择项
2025-06-30 00:20:18 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:20:18 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:24:16 | INFO | QuotaCreationTool | 日志管理器初始化完成
2025-06-30 00:24:16 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:24:16 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:24:16 | INFO | QuotaCreationTool.MainApp | 定额创建工具主应用初始化完成
2025-06-30 00:24:16 | INFO | QuotaCreationTool.AIModelProcessor | 成功刷新LM Studio模型: 6 个模型
2025-06-30 00:24:16 | INFO | QuotaCreationTool.AIModelProcessor | AI模型处理器初始化完成
2025-06-30 00:24:16 | INFO | QuotaCreationTool.MainApp | 🔄 界面创建时加载了 1 个已存储的定额PDF
