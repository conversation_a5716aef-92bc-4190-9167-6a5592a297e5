{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZV2ozN0JkcngtZ2h5RnlIcjRNNm03RXVlZ002aFNOT3NGR2tRNk9VZVJkUkI5QzAxY0ZwVzVPNDdubC0ybEd3NTFXWWZrblo1dEpSNFVhZ1NRNllfaEZ2emFOdXEzX1FhYk9HZkJsLUhZS2pkZkw2TDBvNG1BQlVpTU1ac2lRSFFjcXM=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZV2ozVmduaF9uZTJfdUV1TFFFOVpycldxN3dnVF9VbTdUa3Fwclo4RzA5azNpbFMzRGJwdDNIbTI5LVFCaUxsQ0dRcERUaVlKZ0x4UTVjTEZzd05GRnAwcXc9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T00:25:27.715693", "access_count": 5}}