{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZV3EtMHNTVkdUTldjMFJaVU5hUFVzOWJNMDkwUmJBeGVGNDdfZzhDb1pQeWhSZEZhdjFnYUxBQ2dDRHV5SVh4ZG5rVHpNX19zQ25MVlgzS2JYOTNLTjdHVkpUTVE3WHFNUVlxUWFTazlENEIzU2E0S0JnREMzNFVwaVpac1RpMHBjeC0=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZV3EtMTdNay01N0RFcWJKemVySTRjWHlfWFUzclp0aVFCRXpMUVRJX1d2aGxiTHd4cE9nQXlCQXRjRGozSU9NYjkxbmh3dXlHSkx1SGVBZFJka21pUzZuU3c9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T00:33:02.693027", "access_count": 6}}