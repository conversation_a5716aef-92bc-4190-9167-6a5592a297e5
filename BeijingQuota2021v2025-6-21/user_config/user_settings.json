{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZVmNKbVR5YjBhbXBxOG9oZmI4c0FYaFl6SkVCZ2tWTjJuWlp6czduUWQyU09SOFg0ZTVyLVpDdlVhRFhzbC1IdDRPcVJEU19wWm5Yc3dUblRuaGZJd28xcTVYQXlZTzFmb1ZiMGhnYXdvNlE1OV8zZmdScDdWVUt0c1F0RDNhVEw5Yno=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZVmNKYXFtSjd3YUhmeHA0Rmh3aEZXdWFMUk1mYXlXY2pveVA4Q0F4Y0ozUzVrNlp3TGZ2MkozNHFOakRuaGwzLVVSU3BSNS1WVkRQSjlEaFkyTnZRc2dWNlE9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-29T23:08:57.742278", "access_count": 5}}