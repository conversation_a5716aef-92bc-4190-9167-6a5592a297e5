{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZVlNlbko2NVNrU2V3bVJtNWIzMERiVDR5WWJhN3Z1T01IS2Qwb1dDbnNvTXJURnhXSWhuM1ZvdlNpaUhwbS1oVkZoenAwTnBWeEV1VDJIS2dNdUZhNnpNQmRudGR2NGU1NWd0eXRkVGcxT0JVR3lmYXhuWmFwcjFSdlh4S1J4LWpNODY=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_database", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZVlNlZmtHNTNLVE9RUTVwaGZ4dlVDQzlKUWljbE95WkdSNWRIZkFraXZWN1FKeEZZR1B3S3RxWjViaG5aV2hxZTUxT3IzQ0VWX1VlcF85Mm9mTjFYbm84M1E9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-29T22:58:38.883987", "access_count": 3}}