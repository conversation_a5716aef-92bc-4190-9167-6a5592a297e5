{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZVTZDWXBaV2F3NU9GTzZoWXRpZF9qYXE2djdKXzJTSm5WMVI5U0E5YnFoSUxXQVFDazd0ZTRoSGZyQUNmSHJzSXp6akhILUtnQ2RjaGN0NlpjVE9SZ1Z2NEdWQjJJN2RQT2J3N05TVFJWSTE2UW42dkFBMlFtRTdaaVc3VUhFNTNqSVo=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZVTZDR1ZTX1NlZnBzYk1lVW1lLUNMZVU2bEN2TDcyR3Rwd1F3NjI5U0J2OVJJZUt2SmlERzBNTEhBTlFaRXBIVWxSbGZhQVR2NDczLWZWNlpIc3ltZXdxb2c9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-29T22:32:34.463685", "access_count": 2}}