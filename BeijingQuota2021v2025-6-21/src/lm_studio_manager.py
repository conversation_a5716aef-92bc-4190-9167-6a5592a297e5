#!/usr/bin/env python3
"""
LM Studio模型管理器
LM Studio Model Manager
"""

import requests
import logging
from typing import List, Dict, Tuple, Optional
import time

class LMStudioManager:
    """LM Studio模型管理器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:1234"):
        self.base_url = base_url
        self.logger = logging.getLogger(__name__)
        
    def is_server_running(self) -> bool:
        """检查LM Studio服务器是否运行"""
        try:
            response = requests.get(f"{self.base_url}/v1/models", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def get_available_models(self) -> List[Dict[str, str]]:
        """获取LM Studio中可用的模型列表"""
        try:
            if not self.is_server_running():
                return []
            
            response = requests.get(f"{self.base_url}/v1/models", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = []
                
                for model in data.get("data", []):
                    model_id = model.get("id", "")
                    if model_id:
                        # 提取模型的友好名称
                        friendly_name = self._extract_friendly_name(model_id)
                        models.append({
                            "id": model_id,
                            "name": friendly_name,
                            "full_id": model_id
                        })
                
                return models
            
            return []
            
        except Exception as e:
            self.logger.error(f"获取LM Studio模型列表失败: {str(e)}")
            return []
    
    def _extract_friendly_name(self, model_id: str) -> str:
        """从模型ID提取友好的显示名称"""
        # 移除路径和文件扩展名
        name = model_id.split("/")[-1]
        name = name.replace(".gguf", "").replace(".bin", "")
        
        # 常见模型名称映射
        name_mappings = {
            "qwen2.5-vl-7b": "Qwen2.5-VL-7B (视觉语言)",
            "qwen2-vl-7b": "Qwen2-VL-7B (视觉语言)",
            "llava-v1.6-mistral-7b": "LLaVA-v1.6-Mistral-7B (视觉)",
            "llava-v1.5-7b": "LLaVA-v1.5-7B (视觉)",
            "llava-v1.6-vicuna-7b": "LLaVA-v1.6-Vicuna-7B (视觉)",
            "cogvlm-chat-17b": "CogVLM-Chat-17B (视觉对话)",
            "internvl-chat-v1-5": "InternVL-Chat-v1.5 (视觉对话)",
            "minicpm-v-2_6": "MiniCPM-V-2.6 (小型视觉)",
            "yi-vl-6b": "Yi-VL-6B (视觉语言)",
            "deepseek-vl-7b": "DeepSeek-VL-7B (视觉语言)",
            "monkeyocr": "MonkeyOCR (OCR专用)",
            "monkey-chat": "Monkey-Chat (视觉对话)",
            "phi-3-vision": "Phi-3-Vision (微软视觉)",
            "moondream2": "Moondream2 (轻量视觉)",
            "bakllava": "BakLLaVA (视觉语言)",
            "obsidian": "Obsidian (视觉模型)",
            "llama-3.2-vision": "Llama-3.2-Vision (Meta视觉)",
            "pixtral": "Pixtral (Mistral视觉)"
        }
        
        # 检查是否有匹配的映射
        name_lower = name.lower()
        for key, friendly_name in name_mappings.items():
            if key in name_lower:
                return friendly_name
        
        # 如果没有匹配，尝试智能提取
        if "vl" in name_lower or "vision" in name_lower:
            return f"{name} (视觉语言模型)"
        elif "ocr" in name_lower:
            return f"{name} (OCR模型)"
        elif "chat" in name_lower:
            return f"{name} (对话模型)"
        else:
            return name
    
    def get_vision_models(self) -> List[Dict[str, str]]:
        """获取支持视觉的模型列表"""
        all_models = self.get_available_models()
        vision_models = []
        
        for model in all_models:
            model_id_lower = model["id"].lower()
            # 检查是否为视觉模型
            vision_keywords = [
                "vl", "vision", "llava", "cogvlm", "internvl", 
                "minicpm-v", "yi-vl", "deepseek-vl", "monkey", 
                "phi-3-vision", "moondream", "bakllava", "obsidian",
                "pixtral", "qwen2.5-vl", "qwen2-vl", "ocr"
            ]
            
            if any(keyword in model_id_lower for keyword in vision_keywords):
                vision_models.append(model)
        
        return vision_models
    
    def test_model_connection(self, model_id: str) -> Tuple[bool, str]:
        """测试特定模型的连接"""
        try:
            if not self.is_server_running():
                return False, "❌ LM Studio服务器未运行"
            
            # 获取模型列表验证模型是否存在
            models = self.get_available_models()
            model_exists = any(model["id"] == model_id for model in models)
            
            if not model_exists:
                return False, f"❌ 模型 '{model_id}' 未在LM Studio中加载"
            
            # 尝试发送测试请求
            test_payload = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello, this is a connection test."
                    }
                ],
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return True, f"✅ 模型 '{model_id}' 连接成功"
            else:
                return False, f"❌ 模型响应错误: {response.status_code}"
                
        except Exception as e:
            return False, f"❌ 连接测试失败: {str(e)}"
    
    def get_model_info(self, model_id: str) -> Optional[Dict]:
        """获取特定模型的详细信息"""
        try:
            models = self.get_available_models()
            for model in models:
                if model["id"] == model_id:
                    return model
            return None
        except Exception:
            return None
    
    def refresh_models(self) -> Tuple[bool, str, List[Dict[str, str]]]:
        """刷新模型列表"""
        try:
            if not self.is_server_running():
                return False, "❌ LM Studio服务器未运行，请启动LM Studio并加载模型", []
            
            models = self.get_available_models()
            vision_models = self.get_vision_models()
            
            if not models:
                return False, "❌ LM Studio中没有加载任何模型", []
            
            if not vision_models:
                return False, f"⚠️ 找到 {len(models)} 个模型，但没有视觉语言模型", models
            
            return True, f"✅ 成功获取 {len(models)} 个模型，其中 {len(vision_models)} 个支持视觉", vision_models
            
        except Exception as e:
            return False, f"❌ 刷新模型列表失败: {str(e)}", []
    
    def get_server_status(self) -> Dict[str, any]:
        """获取LM Studio服务器状态"""
        try:
            if not self.is_server_running():
                return {
                    "running": False,
                    "message": "LM Studio服务器未运行",
                    "models_count": 0,
                    "vision_models_count": 0
                }
            
            models = self.get_available_models()
            vision_models = self.get_vision_models()
            
            return {
                "running": True,
                "message": "LM Studio服务器运行正常",
                "models_count": len(models),
                "vision_models_count": len(vision_models),
                "base_url": self.base_url
            }
            
        except Exception as e:
            return {
                "running": False,
                "message": f"检查服务器状态失败: {str(e)}",
                "models_count": 0,
                "vision_models_count": 0
            }
