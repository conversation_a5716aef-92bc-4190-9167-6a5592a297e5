#!/usr/bin/env python3
"""
企业定额管理系统 v2.0
Enterprise Quota Management System v2.0
基于MCP数据库转换工具构建，避免重复造轮子
"""

import os
import sqlite3
import pandas as pd
import json
import re
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime

# 尝试多种导入方式以适应不同的运行环境
try:
    from .mcp_database_converter import MCPDatabaseConverter
except ImportError:
    try:
        from mcp_database_converter import MCPDatabaseConverter
    except ImportError:
        import sys
        sys.path.append(os.path.dirname(__file__))
        from mcp_database_converter import MCPDatabaseConverter

class EnterpriseQuotaManager:
    """企业定额管理系统类（基于MCP工具）"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = None
        self.connection = None
        
        # 集成MCP数据库转换工具
        self.mcp_converter = MCPDatabaseConverter()

        # 集成智能定额处理器
        try:
            from .intelligent_quota_processor import IntelligentQuotaProcessor
            self.intelligent_processor = IntelligentQuotaProcessor()
        except ImportError:
            self.intelligent_processor = None
            self.logger.warning("智能定额处理器未找到，将使用基础处理模式")

        # 支持的数据库类型（基于MCP工具）
        self.supported_db_types = self.mcp_converter.supported_formats

    def _validate_csv_files(self, csv_files: List[str]) -> List[str]:
        """验证CSV文件的有效性"""
        valid_files = []
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_file, encoding='utf-8-sig')
                    if not df.empty:
                        valid_files.append(csv_file)
                        self.logger.info(f"✅ 验证文件: {os.path.basename(csv_file)} ({len(df)} 行)")
                    else:
                        self.logger.warning(f"⚠️ 空文件: {os.path.basename(csv_file)}")
                except Exception as e:
                    self.logger.error(f"❌ 文件读取失败: {os.path.basename(csv_file)} - {e}")
            else:
                self.logger.error(f"❌ 文件不存在: {csv_file}")

        self.logger.info(f"📊 文件验证完成: {len(valid_files)}/{len(csv_files)} 个文件有效")
        return valid_files

    def create_quota_database(
        self,
        db_type: str,
        db_config: Dict[str, str],
        parent_quotas_files: List[str],
        child_resources_files: List[str],
        merge_strategy: str = "smart_merge"
    ) -> Tuple[bool, str]:
        """创建企业定额数据库（基于MCP工具和智能处理器）"""
        try:
            # 合并所有CSV文件
            all_csv_files = parent_quotas_files + child_resources_files

            if not all_csv_files:
                return False, "请至少选择一个CSV文件"

            # 使用智能处理器进行预处理（如果可用）
            processed_files = all_csv_files
            processing_message = ""

            if self.intelligent_processor and (parent_quotas_files and child_resources_files):
                self.logger.info("使用智能处理器进行数据预处理...")
                success, message, stats = self.intelligent_processor.process_quota_files(all_csv_files)

                if success:
                    # 使用处理后的文件（优先使用分离的定额项和资源文件）
                    final_files = stats.get('final_files', [])

                    # 筛选出用于数据库创建的文件（排除汇总文件）
                    db_files = []
                    for file_path in final_files:
                        filename = os.path.basename(file_path)
                        if filename.startswith('processed_parent_quotas_') or filename.startswith('processed_child_resources_'):
                            db_files.append(file_path)

                    # 如果有处理后的分离文件，使用它们；否则使用所有输出文件
                    processed_files = db_files if db_files else final_files
                    processing_message = f"\n🧠 智能处理完成:\n{message}\n"
                    self.logger.info(f"智能处理成功，使用 {len(processed_files)} 个处理后的文件")
                else:
                    self.logger.warning(f"智能处理失败，使用原始文件: {message}")
                    processing_message = f"\n⚠️ 智能处理失败，使用基础模式: {message}\n"



            # 根据数据库类型确定输出路径
            if db_type == 'sqlite':
                output_path = db_config.get('database_path', 'output/enterprise_quota.db')

                # 验证CSV文件
                valid_files = self._validate_csv_files(processed_files)
                if not valid_files:
                    return False, "❌ 未找到有效的定额项或资源文件"

                # 使用MCP工具创建SQLite数据库
                success, message, stats = self.mcp_converter.convert_to_sqlite(valid_files, output_path, merge_strategy)

            elif db_type == 'mongodb':
                # 验证CSV文件
                valid_files = self._validate_csv_files(processed_files)
                if not valid_files:
                    return False, "❌ 未找到有效的定额项或资源文件"

                # 使用MCP工具创建MongoDB JSON导出文件
                output_path = db_config.get('database_path', 'output/mongodb_export.json')
                success, message, stats = self.mcp_converter.convert_to_mongodb(valid_files, output_path, merge_strategy)

            elif db_type == 'postgresql':
                # 直接创建PostgreSQL数据库
                success, message = self._create_postgresql_database_direct(
                    db_config, processed_files
                )
                # PostgreSQL不需要设置output_path，因为数据库在服务器上

            elif db_type == 'mysql':
                # 直接创建MySQL数据库
                success, message = self._create_mysql_database_direct(
                    db_config, processed_files
                )
                # MySQL不需要设置output_path，因为数据库在服务器上

            elif db_type in ['sql_server', 'oracle']:
                output_path = db_config.get('database_path', f'output/enterprise_quota_{db_type}.sql')
                # 使用MCP工具创建SQL脚本
                success, message, stats = self.mcp_converter.convert_to_sql_script(processed_files, output_path, db_type)

            else:
                return False, f"不支持的数据库类型: {db_type}"
            
            if success:
                # 保存数据库路径以便后续查询
                if db_type == 'postgresql':
                    self.db_path = f"{db_config.get('host', 'localhost')}:{db_config.get('port', 5432)}/{db_config.get('database', 'enterprise_quota')}"
                elif db_type == 'mysql':
                    self.db_path = f"{db_config.get('host', 'localhost')}:{db_config.get('port', 3306)}/{db_config.get('database', 'enterprise_quota')}"
                elif db_type == 'mongodb':
                    self.db_path = f"{db_config.get('host', 'localhost')}:{db_config.get('port', 27017)}/{db_config.get('database', 'enterprise_quota')}"
                elif db_type == 'sqlite':
                    self.db_path = output_path
                else:
                    # 对于SQL脚本类型，使用output_path
                    self.db_path = output_path
                
                # 增强成功消息，添加企业定额管理特色
                enhanced_message = f"""✅ 企业定额数据库创建成功！
{processing_message}
📊 数据库转换结果:
{message}

🏢 企业定额管理功能:
- 定额项与资源关联关系已建立
- 支持定额项搜索和资源查看
- 支持价格计算和数据导出
- 可通过查询管理系统进行操作

💡 下一步: 使用"定额查询管理系统"连接并管理此数据库"""
                
                return True, enhanced_message
            else:
                return False, message
            
        except Exception as e:
            error_msg = f"创建定额数据库失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def connect_to_database(self, db_path: str) -> Tuple[bool, str]:
        """连接到定额数据库（使用MCP工具）"""
        try:
            if not os.path.exists(db_path):
                return False, "数据库文件不存在"
            
            # 判断数据库类型
            file_ext = os.path.splitext(db_path)[1].lower()
            
            if file_ext in ['.db', '.sqlite']:
                # SQLite数据库
                self.connection = sqlite3.connect(db_path)
                self.db_path = db_path
                
                # 验证数据库结构（检查是否有表）
                cursor = self.connection.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                if not tables:
                    return False, "数据库中没有找到任何表"
                
                return True, f"SQLite数据库连接成功，找到 {len(tables)} 个表"
                
            elif file_ext == '.json':
                # MongoDB JSON文件
                self.db_path = db_path
                return True, "MongoDB JSON文件连接成功"
                
            else:
                return False, f"不支持的数据库文件格式: {file_ext}"
            
        except Exception as e:
            return False, f"连接数据库失败: {str(e)}"
    
    def preview_database(self, db_path: str) -> str:
        """预览数据库内容（使用MCP工具）"""
        try:
            # 使用MCP工具的预览功能
            return self.mcp_converter.preview_database_file(db_path)
        except Exception as e:
            return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"
    
    def get_database_statistics(self) -> Tuple[bool, str, Dict]:
        """获取数据库统计信息（使用MCP工具）"""
        try:
            if not self.db_path:
                return False, "未连接到数据库", {}
            
            file_ext = os.path.splitext(self.db_path)[1].lower()
            
            if file_ext in ['.db', '.sqlite'] and self.connection:
                # SQLite数据库统计
                cursor = self.connection.cursor()
                
                # 获取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_rows = 0
                table_stats = []
                
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                    count = cursor.fetchone()[0]
                    total_rows += count
                    table_stats.append({'name': table, 'rows': count})
                
                # 获取数据库文件大小
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0

                # 计算定额项和资源数量
                quota_count = 0
                resource_count = 0
                linked_quota_count = 0

                for table_stat in table_stats:
                    table_name = table_stat['name'].lower()
                    if 'parent' in table_name or 'quota' in table_name:
                        quota_count += table_stat['rows']
                    elif 'child' in table_name or 'resource' in table_name:
                        resource_count += table_stat['rows']

                # 估算关联定额项数量
                linked_quota_count = min(quota_count, resource_count // 5) if resource_count > 0 else quota_count

                stats = {
                    'database_type': 'SQLite',
                    'database_path': self.db_path,
                    'total_tables': len(tables),
                    'total_rows': total_rows,
                    'quota_count': quota_count,
                    'resource_count': resource_count,
                    'linked_quota_count': linked_quota_count,
                    'db_size_kb': db_size / 1024,
                    'last_updated': None,  # SQLite没有内置的最后更新时间
                    'tables': table_stats
                }
                
                return True, "统计信息获取成功", stats
                
            elif ':' in self.db_path and ('27017' in self.db_path or 'mongodb' in self.db_path.lower()):
                # MongoDB数据库统计
                try:
                    from pymongo import MongoClient

                    # 解析连接信息
                    parts = self.db_path.split('/')
                    host_port = parts[0]
                    database_name = parts[1] if len(parts) > 1 else 'enterprise_quota'

                    host, port = host_port.split(':')
                    port = int(port)

                    # 连接MongoDB
                    client = MongoClient(f"mongodb://{host}:{port}/")
                    db = client[database_name]

                    # 获取集合列表
                    collection_names = db.list_collection_names()

                    total_documents = 0
                    quota_count = 0
                    resource_count = 0
                    collection_stats = []

                    for coll_name in collection_names:
                        collection = db[coll_name]
                        doc_count = collection.count_documents({})
                        total_documents += doc_count

                        collection_stats.append({
                            'name': coll_name,
                            'documents': doc_count
                        })

                        # 根据集合名称分类
                        if 'parent' in coll_name.lower() or 'quota' in coll_name.lower():
                            quota_count += doc_count
                        elif 'child' in coll_name.lower() or 'resource' in coll_name.lower():
                            resource_count += doc_count

                    # 估算关联定额项数量
                    linked_quota_count = min(quota_count, resource_count // 5) if resource_count > 0 else quota_count

                    # 获取数据库统计信息
                    db_stats = db.command("dbStats")
                    db_size_bytes = db_stats.get('dataSize', 0)

                    stats = {
                        'database_type': 'MongoDB',
                        'database_path': self.db_path,
                        'total_collections': len(collection_names),
                        'total_documents': total_documents,
                        'quota_count': quota_count,
                        'resource_count': resource_count,
                        'linked_quota_count': linked_quota_count,
                        'db_size_kb': db_size_bytes / 1024,
                        'last_updated': datetime.now().isoformat(),
                        'collections': collection_stats
                    }

                    client.close()
                    return True, "统计信息获取成功", stats

                except Exception as e:
                    return False, f"MongoDB统计信息获取失败: {str(e)}", {}

            elif file_ext == '.json':
                # MongoDB JSON文件统计（备用）
                with open(self.db_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                if 'collections' in data:
                    # MongoDB导出格式
                    collections = data['collections']
                    total_documents = sum(len(coll.get('documents', [])) for coll in collections.values())

                    # 计算定额项和资源数量
                    quota_count = 0
                    resource_count = 0
                    linked_quota_count = 0

                    for name, coll in collections.items():
                        doc_count = len(coll.get('documents', []))
                        if 'parent' in name.lower() or 'quota' in name.lower():
                            quota_count += doc_count
                        elif 'child' in name.lower() or 'resource' in name.lower():
                            resource_count += doc_count

                    # 估算关联定额项数量（假设有资源的定额项都是关联的）
                    linked_quota_count = min(quota_count, resource_count // 5) if resource_count > 0 else quota_count

                    stats = {
                        'database_type': 'MongoDB JSON',
                        'database_path': self.db_path,
                        'total_collections': len(collections),
                        'total_documents': total_documents,
                        'quota_count': quota_count,
                        'resource_count': resource_count,
                        'linked_quota_count': linked_quota_count,
                        'db_size_kb': os.path.getsize(self.db_path) / 1024,
                        'last_updated': data.get('metadata', {}).get('export_date', None),
                        'collections': [
                            {'name': name, 'documents': len(coll.get('documents', []))}
                            for name, coll in collections.items()
                        ]
                    }
                else:
                    # 普通JSON文件
                    stats = {
                        'database_type': 'JSON',
                        'database_path': self.db_path,
                        'db_size_kb': os.path.getsize(self.db_path) / 1024
                    }

                return True, "统计信息获取成功", stats
            
            else:
                return False, "不支持的数据库类型", {}
            
        except Exception as e:
            return False, f"获取统计信息失败: {str(e)}", {}

    def _create_mongodb_database_direct(
        self,
        db_config: Dict[str, str],
        csv_files: List[str]
    ) -> Tuple[bool, str]:
        """直接创建MongoDB数据库"""
        try:
            from pymongo import MongoClient

            # 获取连接配置
            host = db_config.get('host', 'localhost')
            port = int(db_config.get('port', 27017))
            database_name = db_config.get('database', 'enterprise_quota')
            username = db_config.get('username', '')
            password = db_config.get('password', '')

            # 清理数据库名称，移除不允许的字符
            # MongoDB数据库名不能包含: / \ . " * < > : | ? 空格
            database_name = re.sub(r'[/\\."*<>:|?\s]', '_', database_name)
            # 确保数据库名不为空且不以数字开头
            if not database_name or database_name[0].isdigit():
                database_name = 'enterprise_quota_' + database_name

            print(f"📊 数据库名称: {database_name}")

            # 构建连接字符串
            if username and password:
                connection_string = f"mongodb://{username}:{password}@{host}:{port}/"
            else:
                connection_string = f"mongodb://{host}:{port}/"

            print(f"🔗 连接MongoDB: {connection_string}")

            # 连接MongoDB
            client = MongoClient(connection_string)
            db = client[database_name]

            # 验证连接
            client.admin.command('ping')
            print(f"✅ MongoDB连接成功")

            total_documents = 0
            collections_created = []

            # 处理每个CSV文件
            for csv_path in csv_files:
                if not os.path.exists(csv_path):
                    print(f"⚠️ 文件不存在: {csv_path}")
                    continue

                print(f"📄 处理文件: {os.path.basename(csv_path)}")

                # 分析CSV结构
                structure = self.mcp_converter.analyze_csv_structure(csv_path)
                if not structure:
                    print(f"❌ 无法分析文件结构: {csv_path}")
                    continue

                # 生成集合名
                collection_name = self.mcp_converter.generate_table_name(structure['filename'])
                print(f"📊 创建集合: {collection_name}")

                # 获取或创建集合
                collection = db[collection_name]

                # 清空现有数据（如果存在）
                collection.delete_many({})

                # 转换数据为MongoDB文档
                df = structure['dataframe']
                documents = []

                for index, row in df.iterrows():
                    document = {}
                    for col_name, value in row.items():
                        # 清理字段名（MongoDB字段名规则）
                        clean_field_name = str(col_name)
                        # 只替换可能导致问题的字符，保留中文
                        clean_field_name = re.sub(r'[.$]', '_', clean_field_name)
                        # 如果字段名以数字开头，添加前缀
                        if clean_field_name and clean_field_name[0].isdigit():
                            clean_field_name = 'field_' + clean_field_name
                        # 如果字段名为空或只有特殊字符，使用默认名称
                        if not clean_field_name or clean_field_name.isspace():
                            clean_field_name = f'field_{len(document)}'

                        # 处理值
                        if pd.isna(value):
                            document[clean_field_name] = None
                        elif isinstance(value, (int, float)):
                            # 保持数值类型
                            if pd.isna(value):
                                document[clean_field_name] = None
                            else:
                                document[clean_field_name] = value
                        else:
                            # 字符串类型
                            document[clean_field_name] = str(value)

                    # 添加元数据
                    document['_source_file'] = structure['filename']
                    document['_import_date'] = datetime.now().isoformat()
                    document['_row_index'] = index

                    documents.append(document)

                # 批量插入文档
                if documents:
                    result = collection.insert_many(documents)
                    inserted_count = len(result.inserted_ids)
                    total_documents += inserted_count
                    collections_created.append({
                        'name': collection_name,
                        'documents': inserted_count,
                        'source_file': structure['filename']
                    })
                    print(f"✅ 插入 {inserted_count} 个文档到集合 {collection_name}")
                else:
                    print(f"⚠️ 文件 {csv_path} 没有有效数据")

            # 创建索引
            print(f"🔍 创建索引...")
            for coll_info in collections_created:
                collection = db[coll_info['name']]

                # 为定额项集合创建索引
                if 'parent' in coll_info['name'].lower() or 'quota' in coll_info['name'].lower():
                    try:
                        # 尝试为编号字段创建唯一索引
                        for field_name in ['编号', 'quota_code', '定额编号']:
                            try:
                                collection.create_index(field_name, unique=True)
                                print(f"   ✅ 为 {coll_info['name']}.{field_name} 创建唯一索引")
                                break
                            except:
                                continue
                    except Exception as e:
                        print(f"   ⚠️ 创建索引失败: {e}")

                # 为资源集合创建索引
                elif 'child' in coll_info['name'].lower() or 'resource' in coll_info['name'].lower():
                    try:
                        # 为定额编号创建索引
                        for field_name in ['定额编号', 'quota_code', '编号']:
                            try:
                                collection.create_index(field_name)
                                print(f"   ✅ 为 {coll_info['name']}.{field_name} 创建索引")
                                break
                            except:
                                continue
                    except Exception as e:
                        print(f"   ⚠️ 创建索引失败: {e}")

            client.close()

            success_msg = f"""✅ MongoDB数据库创建成功！
📊 数据库信息:
   - 数据库名: {database_name}
   - 集合数量: {len(collections_created)}
   - 文档总数: {total_documents}

📋 集合详情:"""

            for coll_info in collections_created:
                success_msg += f"\n   - {coll_info['name']}: {coll_info['documents']} 个文档 (来源: {coll_info['source_file']})"

            success_msg += f"\n\n🔗 连接信息: {host}:{port}/{database_name}"

            return True, success_msg

        except Exception as e:
            error_msg = f"MongoDB数据库创建失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def _create_postgresql_database_direct(
        self,
        db_config: Dict[str, str],
        csv_files: List[str]
    ) -> Tuple[bool, str]:
        """直接创建PostgreSQL数据库"""
        try:
            import psycopg2
            from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

            # 获取连接配置
            host = db_config.get('host', 'localhost')
            port = int(db_config.get('port', 5432))
            user = db_config.get('user', 'postgres')
            password = db_config.get('password', '')
            default_db = db_config.get('default_db', 'postgres')
            target_db = db_config.get('database', 'enterprise_quota')

            # 连接到默认数据库
            conn = psycopg2.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=default_db
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()

            # 检查目标数据库是否存在
            cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (target_db,))
            db_exists = cursor.fetchone() is not None

            if not db_exists:
                # 创建数据库
                cursor.execute(f'CREATE DATABASE "{target_db}"')
                print(f"✅ 创建数据库: {target_db}")
            else:
                print(f"ℹ️ 数据库已存在: {target_db}")

            cursor.close()
            conn.close()

            # 连接到目标数据库并创建表
            target_conn = psycopg2.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=target_db
            )
            target_cursor = target_conn.cursor()

            # 使用MCP工具生成SQL并执行
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as temp_file:
                temp_sql_path = temp_file.name

            # 验证CSV文件
            valid_files = []
            for csv_file in csv_files:
                if os.path.exists(csv_file):
                    try:
                        import pandas as pd
                        df = pd.read_csv(csv_file, encoding='utf-8-sig')
                        if not df.empty:
                            valid_files.append(csv_file)
                            self.logger.info(f"✅ 验证文件: {csv_file} ({len(df)} 行)")
                        else:
                            self.logger.warning(f"⚠️ 空文件: {csv_file}")
                    except Exception as e:
                        self.logger.error(f"❌ 文件读取失败: {csv_file} - {e}")
                else:
                    self.logger.error(f"❌ 文件不存在: {csv_file}")

            if not valid_files:
                return False, "❌ 未找到有效的定额项或资源文件"

            # 生成SQL脚本
            self.logger.info(f"正在为 {len(valid_files)} 个有效文件生成PostgreSQL SQL脚本")
            success, message, stats = self.mcp_converter.convert_to_sql_script(
                valid_files, temp_sql_path, 'postgresql'
            )

            if success:
                self.logger.info(f"SQL脚本生成成功: {message}")
                # 读取并执行SQL脚本
                with open(temp_sql_path, 'r', encoding='utf-8') as f:
                    sql_content = f.read()

                # 更智能的SQL语句分割和执行
                # 移除注释行
                lines = sql_content.split('\n')
                clean_lines = []
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('--'):
                        clean_lines.append(line)

                clean_sql = '\n'.join(clean_lines)

                # 分割SQL语句
                sql_statements = []
                current_statement = []

                for line in clean_lines:
                    current_statement.append(line)
                    if line.endswith(';'):
                        stmt = '\n'.join(current_statement).strip()
                        if stmt:
                            sql_statements.append(stmt)
                        current_statement = []

                # 处理最后一个语句（如果没有分号结尾）
                if current_statement:
                    stmt = '\n'.join(current_statement).strip()
                    if stmt:
                        sql_statements.append(stmt)

                executed_count = 0
                self.logger.info(f"准备执行 {len(sql_statements)} 个SQL语句")

                for i, stmt in enumerate(sql_statements):
                    if stmt.strip():
                        try:
                            self.logger.debug(f"执行SQL语句 {i+1}: {stmt[:100]}...")
                            target_cursor.execute(stmt)
                            executed_count += 1
                        except Exception as e:
                            error_msg = f"⚠️ SQL执行错误 {i+1}: {e}"
                            print(error_msg)
                            self.logger.error(f"{error_msg}\nSQL语句: {stmt}")
                            # 继续执行其他语句

                self.logger.info(f"成功执行 {executed_count} 个SQL语句")

                target_conn.commit()

                # 获取表统计信息
                target_cursor.execute("""
                    SELECT table_name,
                           (SELECT COUNT(*) FROM information_schema.columns
                            WHERE table_name = t.table_name AND table_schema = 'public') as column_count
                    FROM information_schema.tables t
                    WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                """)

                tables_info = target_cursor.fetchall()

                # 获取总行数
                total_rows = 0
                table_details = []
                for table_name, column_count in tables_info:
                    target_cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                    row_count = target_cursor.fetchone()[0]
                    total_rows += row_count
                    table_details.append(f"  • {table_name}: {row_count} 行, {column_count} 列")

                # 清理临时文件
                os.unlink(temp_sql_path)

                success_message = f"""✅ PostgreSQL数据库创建成功！

🗄️ 数据库信息:
- 服务器: {host}:{port}
- 数据库: {target_db}
- 用户: {user}

📊 数据统计:
- 表数量: {len(tables_info)} 个
- 总记录数: {total_rows} 行

📋 表详情:
{chr(10).join(table_details)}

🏢 企业定额管理功能:
- 定额项与资源关联关系已建立
- 支持定额项搜索和资源查看
- 支持价格计算和数据导出
- 可通过查询管理系统进行操作

💡 下一步: 使用"定额查询管理系统"连接并管理此数据库"""

                target_cursor.close()
                target_conn.close()

                return True, success_message
            else:
                target_cursor.close()
                target_conn.close()
                return False, f"SQL脚本生成失败: {message}"

        except ImportError:
            return False, "❌ 缺少psycopg2模块，请安装: pip install psycopg2-binary"
        except Exception as e:
            return False, f"❌ PostgreSQL数据库创建失败: {str(e)}"

    def _create_mysql_database_direct(
        self,
        db_config: Dict[str, str],
        csv_files: List[str]
    ) -> Tuple[bool, str]:
        """直接创建MySQL数据库"""
        try:
            import pymysql

            # 获取连接配置
            host = db_config.get('host', 'localhost')
            port = int(db_config.get('port', 3306))
            user = db_config.get('user', 'root')
            password = db_config.get('password', '')
            target_db = db_config.get('database', 'enterprise_quota')

            # 连接到MySQL服务器（不指定数据库）- MySQL 8.0兼容配置
            conn = pymysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                charset='utf8mb4',
                autocommit=True,
                connect_timeout=30,
                read_timeout=30,
                write_timeout=30,
                sql_mode='TRADITIONAL',  # 兼容MySQL 8.0的严格模式
                init_command="SET SESSION sql_mode='TRADITIONAL,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"
            )
            cursor = conn.cursor()

            # 检查MySQL版本以确保兼容性
            cursor.execute("SELECT VERSION()")
            mysql_version = cursor.fetchone()[0]
            print(f"🔍 检测到MySQL版本: {mysql_version}")

            # 解析版本号
            version_parts = mysql_version.split('.')
            major_version = int(version_parts[0])
            minor_version = int(version_parts[1]) if len(version_parts) > 1 else 0

            # 根据版本选择合适的排序规则
            if major_version >= 8:
                collation = 'utf8mb4_0900_ai_ci'  # MySQL 8.0+推荐
                print("✅ 使用MySQL 8.0+优化配置")
            else:
                collation = 'utf8mb4_unicode_ci'  # MySQL 5.7兼容
                print("✅ 使用MySQL 5.7兼容配置")

            # 检查目标数据库是否存在
            cursor.execute("SHOW DATABASES LIKE %s", (target_db,))
            db_exists = cursor.fetchone() is not None

            if not db_exists:
                # 创建数据库 - 根据版本使用合适的配置
                create_db_sql = f"""
                CREATE DATABASE `{target_db}`
                CHARACTER SET utf8mb4
                COLLATE {collation}
                """
                cursor.execute(create_db_sql)
                print(f"✅ 创建数据库: {target_db} (MySQL {major_version}.{minor_version}兼容模式)")
            else:
                print(f"ℹ️ 数据库已存在: {target_db}")

            cursor.close()
            conn.close()

            # 连接到目标数据库并创建表 - MySQL 8.0兼容配置
            target_conn = pymysql.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=target_db,
                charset='utf8mb4',
                autocommit=False,  # 使用事务模式确保数据一致性
                connect_timeout=30,
                read_timeout=30,
                write_timeout=30,
                sql_mode='TRADITIONAL',
                init_command="SET SESSION sql_mode='TRADITIONAL,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'"
            )
            target_cursor = target_conn.cursor()

            # 使用MCP工具生成SQL并执行
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False, encoding='utf-8') as temp_file:
                temp_sql_path = temp_file.name

            # 验证CSV文件
            valid_files = []
            for csv_file in csv_files:
                if os.path.exists(csv_file):
                    try:
                        import pandas as pd
                        df = pd.read_csv(csv_file, encoding='utf-8-sig')
                        if not df.empty:
                            valid_files.append(csv_file)
                            print(f"✅ 验证文件: {os.path.basename(csv_file)} ({len(df)} 行)")
                    except Exception as e:
                        print(f"⚠️ 跳过无效文件: {os.path.basename(csv_file)} - {str(e)}")

            if not valid_files:
                target_cursor.close()
                target_conn.close()
                return False, "❌ 未找到有效的CSV文件"

            # 使用参数化查询直接插入数据（避免SQL注入和字符转义问题）
            success, message, stats = self.mcp_converter.insert_data_to_mysql(valid_files, target_cursor, target_conn)

            # 清理临时文件
            try:
                os.unlink(temp_sql_path)
            except:
                pass

            if success:
                # 从stats获取统计信息
                table_count = stats.get('tables_created', 0)
                total_records = stats.get('total_records', 0)

                success_message = f"""✅ MySQL数据库创建成功！

📊 数据库信息:
- 数据库名: {target_db}
- 服务器: {host}:{port}
- MySQL版本: {major_version}.{minor_version}
- 表数量: {table_count}
- 总记录数: {total_records}
- 字符集: utf8mb4
- 排序规则: {collation}

📁 数据来源:
{chr(10).join([f"- {os.path.basename(f)}" for f in valid_files])}

💡 下一步: 使用"高级定额管理系统"连接并管理此数据库"""

                target_cursor.close()
                target_conn.close()

                return True, success_message
            else:
                target_cursor.close()
                target_conn.close()
                return False, f"数据插入失败: {message}"

        except ImportError:
            return False, "❌ 缺少pymysql模块，请安装: pip install pymysql"
        except Exception as e:
            return False, f"❌ MySQL数据库创建失败: {str(e)}"

    def search_quotas(
        self, 
        search_term: str = "", 
        limit: int = 50,
        offset: int = 0
    ) -> Tuple[bool, str, List[Dict]]:
        """搜索定额项（基于MCP工具创建的数据库）"""
        try:
            if not self.connection or not self.db_path:
                return False, "未连接到数据库", []
            
            file_ext = os.path.splitext(self.db_path)[1].lower()
            
            if file_ext in ['.db', '.sqlite']:
                # SQLite数据库查询
                cursor = self.connection.cursor()
                
                # 获取表名（动态查找包含定额数据的表）
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                # 查找可能的定额表
                quota_table = None
                for table in tables:
                    cursor.execute(f"PRAGMA table_info(`{table}`)")
                    columns = [col[1] for col in cursor.fetchall()]
                    # 如果表包含定额相关字段，认为是定额表
                    if any(col in ['quota_code', '定额编号', 'quota_name', '定额名称'] for col in columns):
                        quota_table = table
                        break
                
                if not quota_table:
                    # 如果没有找到专门的定额表，使用第一个表
                    quota_table = tables[0] if tables else None
                
                if not quota_table:
                    return False, "数据库中没有找到数据表", []
                
                # 构建查询
                if search_term:
                    # 获取表的所有列
                    cursor.execute(f"PRAGMA table_info(`{quota_table}`)")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    # 构建模糊搜索条件
                    search_conditions = []
                    for col in columns:
                        search_conditions.append(f"`{col}` LIKE ?")
                    
                    where_clause = " OR ".join(search_conditions)
                    query = f"SELECT * FROM `{quota_table}` WHERE {where_clause} LIMIT ? OFFSET ?"
                    
                    search_pattern = f"%{search_term}%"
                    params = [search_pattern] * len(columns) + [limit, offset]
                    cursor.execute(query, params)
                else:
                    # 获取所有数据
                    query = f"SELECT * FROM `{quota_table}` LIMIT ? OFFSET ?"
                    cursor.execute(query, (limit, offset))
                
                rows = cursor.fetchall()
                
                # 获取列名
                cursor.execute(f"PRAGMA table_info(`{quota_table}`)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # 转换为字典格式
                quotas = []
                for row in rows:
                    quota = dict(zip(columns, row))
                    quotas.append(quota)
                
                return True, f"找到 {len(quotas)} 个记录", quotas
                
            else:
                return False, "当前只支持SQLite数据库查询", []
            
        except Exception as e:
            return False, f"搜索失败: {str(e)}", []
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
