#!/usr/bin/env python3
"""
智能信息价识别界面
Intelligent Price Information Recognition Interface
基于定额识别界面的设计，提供独立的信息价识别功能界面
"""

import gradio as gr
import pandas as pd
import os
import asyncio
from typing import List, Dict, Any, Optional, Tuple
import logging

from .intelligent_price_info_processor import IntelligentPriceInfoProcessor
from .pdf_storage_manager import PDFStorageManager

class IntelligentPriceInfoInterface:
    """智能信息价识别界面类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processor = IntelligentPriceInfoProcessor()
        self.pdf_storage = PDFStorageManager()
        
    def create_price_info_recognition_interface(self):
        """创建信息价识别界面"""
        with gr.Column():
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">💰</span>智能信息价识别处理
                </h3>
                <p style="color: #666; margin-bottom: 20px;">
                    基于独立的AI模型处理器，智能识别北京市造价信息PDF文件中的价格数据
                </p>
            """)
            
            # API密钥配置区域
            with gr.Group(elem_classes="api-config"):
                gr.HTML("""
                    <h4 style="color: #667eea; margin-bottom: 10px;">
                        <span class="icon">🔑</span>API密钥配置
                    </h4>
                """)
                
                with gr.Row():
                    with gr.Column(scale=2):
                        dashscope_key_input = gr.Textbox(
                            label="🌟 阿里云百炼API密钥 (DASHSCOPE_API_KEY)",
                            placeholder="sk-xxx...",
                            type="password",
                            value=self.processor.api_keys.get("dashscope", "")
                        )
                    with gr.Column(scale=2):
                        openai_key_input = gr.Textbox(
                            label="🤖 OpenAI API密钥 (OPENAI_API_KEY)",
                            placeholder="sk-xxx...",
                            type="password",
                            value=self.processor.api_keys.get("openai", "")
                        )
                    with gr.Column(scale=1):
                        save_keys_btn = gr.Button(
                            "💾 保存密钥",
                            variant="secondary",
                            size="sm"
                        )
                
                # API密钥状态显示
                api_status_output = gr.HTML(
                    label="API状态",
                    value=self._get_api_status_html()
                )
            
            with gr.Row():
                with gr.Column(scale=2):
                    # PDF文件选择
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h4 style="color: #667eea; margin-bottom: 10px;">
                                <span class="icon">📄</span>PDF文件选择
                            </h4>
                        """)

                        with gr.Tabs():
                            with gr.Tab("📤 上传新文件"):
                                price_pdf_input = gr.File(
                                    label="选择造价信息PDF文件",
                                    file_types=[".pdf"],
                                    type="filepath"
                                )

                            with gr.Tab("📁 选择已存储文件"):
                                with gr.Row():
                                    stored_price_pdf_dropdown = gr.Dropdown(
                                        label="选择已存储的信息价PDF",
                                        choices=[],
                                        value=None,
                                        interactive=True,
                                        scale=3
                                    )
                                    refresh_stored_price_btn = gr.Button("🔄 刷新", elem_classes="btn-secondary", size="sm", scale=1)

                                stored_price_pdf_info = gr.HTML(
                                    value="<p style='color: #666; text-align: center;'>选择PDF文件查看详情</p>",
                                    elem_classes="file-info"
                                )
                    
                    # PDF预览区域
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h4 style="color: #667eea; margin-bottom: 10px;">
                                <span class="icon">📖</span>PDF预览浏览器
                            </h4>
                        """)
                        
                        # PDF控制按钮
                        with gr.Row():
                            with gr.Column(scale=1):
                                current_page_input = gr.Number(
                                    label="当前页码",
                                    value=1,
                                    minimum=1,
                                    precision=0,
                                    interactive=True
                                )
                            with gr.Column(scale=1):
                                total_pages_display = gr.Textbox(
                                    label="总页数",
                                    value="0",
                                    interactive=False
                                )
                            with gr.Column(scale=2):
                                with gr.Row():
                                    prev_page_btn = gr.Button("⬅️ 上一页", size="sm")
                                    next_page_btn = gr.Button("➡️ 下一页", size="sm")
                                    zoom_btn = gr.Button("🔍 放大查看", size="sm")

                        # PDF页面显示
                        pdf_viewer = gr.HTML(
                            label="PDF预览",
                            value="""
                                <div style="text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                    <div style="font-size: 3em; margin-bottom: 20px;">📄</div>
                                    <h3 style="color: #667eea; margin-bottom: 10px;">PDF智能浏览器</h3>
                                    <p style="color: #666;">上传PDF文件后将显示交互式浏览器</p>
                                    <p style="color: #999; font-size: 0.9em;">支持页面导航、缩放查看等功能</p>
                                </div>
                            """
                        )

                    # 隐藏状态变量：当前PDF文件路径
                    current_price_pdf_path = gr.State(value=None)

                    # 处理参数设置
                    with gr.Row():
                        start_page_input = gr.Number(
                            label="开始页码",
                            value=1,
                            minimum=1,
                            precision=0
                        )
                        end_page_input = gr.Number(
                            label="结束页码",
                            value=1,
                            minimum=1,
                            precision=0
                        )
                    
                    # AI模型选择和测试
                    with gr.Row():
                        with gr.Column(scale=3):
                            model_type_input = gr.Dropdown(
                                label="🤖 选择AI模型",
                                choices=[
                                    ("阿里通义千问-QVQ-Max", "qwen_qvq_max")
                                ],
                                value=None,
                                info="💡 推荐使用LM Studio本地模型"
                            )
                        with gr.Column(scale=1):
                            with gr.Row():
                                refresh_price_models_btn = gr.Button(
                                    "🔄 刷新模型",
                                    variant="secondary",
                                    size="sm"
                                )
                                test_model_btn = gr.Button(
                                    "🔧 测试连接",
                                    variant="secondary",
                                    size="sm"
                                )

                    # 模型测试结果显示
                    model_test_output = gr.HTML(
                        label="模型连接状态",
                        value="<p style='color: #666;'>点击'🔧 测试连接'检查模型状态</p>"
                    )

                    # LM Studio状态显示
                    price_lm_studio_status = gr.HTML(
                        value="<p style='color: #666;'>正在检查LM Studio状态...</p>",
                        label="LM Studio状态"
                    )

                    # 处理按钮
                    process_price_btn = gr.Button(
                        "🚀 开始识别信息价",
                        variant="primary",
                        size="lg"
                    )
                
                with gr.Column(scale=1):
                    # 处理状态显示
                    price_status_output = gr.HTML(
                        label="处理状态",
                        value="<p style='color: #666;'>等待开始处理...</p>"
                    )
        
        # 结果展示区域
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">📊</span>识别结果
                </h3>
            """)
            
            # 结果统计
            price_stats_output = gr.HTML(
                label="统计信息",
                value="<p style='color: #666;'>暂无数据</p>"
            )
            
            # 结果预览
            price_preview_output = gr.Dataframe(
                label="信息价数据预览",
                headers=["信息价标识", "章节编号", "章节名称", "资源编号", "产品名称",
                        "规格型号及特征", "计量单位", "市场参考价（含税）", "市场参考价（不含税）", "备注"],
                interactive=False,
                wrap=True
            )
            
            # 下载文件组件
            with gr.Row():
                price_download_csv = gr.File(
                    label="📄 CSV文件下载",
                    visible=True,
                    interactive=False
                )
                price_download_json = gr.File(
                    label="📄 JSON文件下载",
                    visible=True,
                    interactive=False
                )

            # 下载状态显示
            price_download_output = gr.HTML(
                label="下载状态",
                value=""
            )

        # 数据库写入功能区域
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">🗄️</span>数据库写入功能
                </h3>
                <p style="color: #666; margin-bottom: 20px;">
                    将识别的信息价数据写入数据库，支持多种数据库类型和智能合并策略
                </p>
            """)

            with gr.Row():
                with gr.Column(scale=1):
                    price_db_type = gr.Dropdown(
                        label="🗄️ 数据库类型",
                        choices=[
                            ("SQLite", "sqlite"),
                            ("MongoDB (JSON)", "mongodb"),
                            ("MySQL", "mysql"),
                            ("PostgreSQL", "postgresql")
                        ],
                        value="sqlite",
                        info="选择目标数据库类型"
                    )

                with gr.Column(scale=2):
                    price_db_name = gr.Textbox(
                        label="📂 数据库名称",
                        placeholder="price_info.db",
                        value="price_info.db",
                        info="💾 数据库文件名或数据库名称"
                    )

                with gr.Column(scale=1):
                    price_merge_strategy = gr.Dropdown(
                        label="📝 合并策略",
                        choices=[
                            ("智能合并（推荐）", "smart_merge"),
                            ("完全覆盖", "replace"),
                            ("添加时间戳", "timestamp")
                        ],
                        value="smart_merge",
                        info="选择数据合并方式"
                    )

            # 数据库连接配置（MySQL/PostgreSQL）
            with gr.Group(visible=False) as price_db_config_group:
                with gr.Row():
                    with gr.Column(scale=2):
                        price_db_host = gr.Textbox(
                            label="🌐 主机地址",
                            placeholder="localhost",
                            value="localhost"
                        )
                    with gr.Column(scale=1):
                        price_db_port = gr.Number(
                            label="🔌 端口",
                            value=3306,
                            precision=0
                        )

                with gr.Row():
                    with gr.Column(scale=1):
                        price_db_user = gr.Textbox(
                            label="👤 用户名",
                            placeholder="root"
                        )
                    with gr.Column(scale=1):
                        price_db_password = gr.Textbox(
                            label="🔐 密码",
                            type="password"
                        )
                    with gr.Column(scale=1):
                        price_default_db = gr.Textbox(
                            label="📊 默认数据库",
                            placeholder="mysql",
                            value="mysql"
                        )

            # 数据库操作按钮和状态
            with gr.Row():
                write_to_database_btn = gr.Button(
                    "🗄️ 写入数据库",
                    variant="primary",
                    size="lg"
                )

            # 数据库操作状态
            price_db_status = gr.HTML(
                label="数据库操作状态",
                value="<p style='color: #666; text-align: center;'>请先完成信息价识别，然后点击写入数据库</p>",
                elem_classes="db-status"
            )

            # 数据库统计信息
            price_db_stats = gr.HTML(
                label="数据库统计",
                value="",
                elem_classes="db-stats"
            )

        # 绑定事件
        self._bind_events(
            save_keys_btn, dashscope_key_input, openai_key_input, api_status_output,
            price_pdf_input, pdf_viewer, current_page_input, total_pages_display,
            prev_page_btn, next_page_btn, zoom_btn,
            model_type_input, test_model_btn, model_test_output,
            process_price_btn, start_page_input, end_page_input,
            price_status_output, price_stats_output, price_preview_output,
            price_download_csv, price_download_json, price_download_output,
            price_db_type, price_db_config_group, write_to_database_btn,
            price_db_name, price_merge_strategy, price_db_host, price_db_port,
            price_db_user, price_db_password, price_default_db,
            price_db_status, price_db_stats,
            refresh_price_models_btn, price_lm_studio_status,
            stored_price_pdf_dropdown, refresh_stored_price_btn, stored_price_pdf_info, current_price_pdf_path
        )
        
        return {
            'dashscope_key': dashscope_key_input,
            'openai_key': openai_key_input,
            'save_keys_btn': save_keys_btn,
            'api_status': api_status_output,
            'pdf_input': price_pdf_input,
            'pdf_viewer': pdf_viewer,
            'current_page': current_page_input,
            'total_pages': total_pages_display,
            'prev_page_btn': prev_page_btn,
            'next_page_btn': next_page_btn,
            'zoom_btn': zoom_btn,
            'start_page': start_page_input,
            'end_page': end_page_input,
            'model_type': model_type_input,
            'test_model_btn': test_model_btn,
            'model_test_output': model_test_output,
            'process_btn': process_price_btn,
            'status_output': price_status_output,
            'stats_output': price_stats_output,
            'preview_output': price_preview_output,
            'download_csv': price_download_csv,
            'download_json': price_download_json,
            'download_output': price_download_output,
            'stored_price_pdf_dropdown': stored_price_pdf_dropdown,
            'refresh_stored_price_btn': refresh_stored_price_btn,
            'stored_price_pdf_info': stored_price_pdf_info,
            'current_price_pdf_path': current_price_pdf_path
        }
    
    def _get_api_status_html(self) -> str:
        """获取API状态HTML"""
        status = self.processor.get_api_key_status()
        
        html = "<div style='padding: 10px; background: #f8f9fa; border-radius: 8px;'>"
        html += "<h5 style='margin: 0 0 10px 0; color: #495057;'>API密钥状态:</h5>"
        
        for provider, is_set in status.items():
            if provider == "dashscope":
                name = "阿里云百炼"
                icon = "🌟"
            elif provider == "openai":
                name = "OpenAI"
                icon = "🤖"
            else:
                name = provider.upper()
                icon = "🔑"
            
            status_icon = "✅" if is_set else "❌"
            status_text = "已设置" if is_set else "未设置"
            color = "#28a745" if is_set else "#dc3545"
            
            html += f"<p style='margin: 5px 0; color: {color};'>{icon} {name}: {status_icon} {status_text}</p>"
        
        html += "</div>"
        return html

    def _bind_events(self, save_keys_btn, dashscope_key_input, openai_key_input, api_status_output,
                     price_pdf_input, pdf_viewer, current_page_input, total_pages_display,
                     prev_page_btn, next_page_btn, zoom_btn,
                     model_type_input, test_model_btn, model_test_output,
                     process_price_btn, start_page_input, end_page_input,
                     price_status_output, price_stats_output, price_preview_output,
                     price_download_csv, price_download_json, price_download_output,
                     price_db_type, price_db_config_group, write_to_database_btn,
                     price_db_name, price_merge_strategy, price_db_host, price_db_port,
                     price_db_user, price_db_password, price_default_db,
                     price_db_status, price_db_stats,
                     refresh_price_models_btn, price_lm_studio_status,
                     stored_price_pdf_dropdown, refresh_stored_price_btn, stored_price_pdf_info, current_price_pdf_path):
        """绑定界面事件"""

        # API密钥保存事件
        save_keys_btn.click(
            fn=self.save_api_keys,
            inputs=[dashscope_key_input, openai_key_input],
            outputs=[api_status_output]
        )

        # 模型连接测试事件
        test_model_btn.click(
            fn=self.test_model_connection,
            inputs=[model_type_input],
            outputs=[model_test_output]
        )

        # PDF文件上传事件
        price_pdf_input.upload(
            fn=self.handle_pdf_upload,
            inputs=[price_pdf_input],
            outputs=[pdf_viewer, current_page_input, total_pages_display, current_price_pdf_path]
        )

        # 已存储PDF相关事件
        refresh_stored_price_btn.click(
            fn=self.get_stored_price_pdfs,
            outputs=[stored_price_pdf_dropdown]
        )

        stored_price_pdf_dropdown.change(
            fn=self.show_stored_price_pdf_info,
            inputs=[stored_price_pdf_dropdown],
            outputs=[stored_price_pdf_info]
        )

        stored_price_pdf_dropdown.change(
            fn=self.load_stored_price_pdf,
            inputs=[stored_price_pdf_dropdown],
            outputs=[pdf_viewer, current_page_input, total_pages_display, current_price_pdf_path]
        )

        # 页面导航事件
        current_page_input.change(
            fn=self.show_page,
            inputs=[current_price_pdf_path, current_page_input],
            outputs=[pdf_viewer]
        )

        prev_page_btn.click(
            fn=self.prev_page,
            inputs=[current_price_pdf_path, current_page_input],
            outputs=[current_page_input, pdf_viewer]
        )

        next_page_btn.click(
            fn=self.next_page,
            inputs=[current_price_pdf_path, current_page_input, total_pages_display],
            outputs=[current_page_input, pdf_viewer]
        )

        zoom_btn.click(
            fn=self.zoom_page,
            inputs=[current_price_pdf_path, current_page_input],
            outputs=[pdf_viewer]
        )

        # 处理按钮事件
        process_price_btn.click(
            fn=self.process_price_info,
            inputs=[
                current_price_pdf_path,
                start_page_input,
                end_page_input,
                model_type_input
            ],
            outputs=[
                price_status_output,
                price_stats_output,
                price_preview_output,
                price_download_csv,
                price_download_json,
                price_download_output
            ]
        )

        # 数据库类型变化事件
        price_db_type.change(
            fn=self.toggle_db_config,
            inputs=[price_db_type],
            outputs=[price_db_config_group]
        )

        # 数据库写入事件
        write_to_database_btn.click(
            fn=self.write_to_database,
            inputs=[
                price_db_type, price_db_name, price_merge_strategy,
                price_db_host, price_db_port, price_db_user,
                price_db_password, price_default_db
            ],
            outputs=[price_db_status, price_db_stats]
        )

        # 刷新模型列表事件
        refresh_price_models_btn.click(
            fn=self.refresh_price_models,
            outputs=[model_type_input, price_lm_studio_status]
        )

    def save_api_keys(self, dashscope_key: str, openai_key: str) -> str:
        """保存API密钥"""
        try:
            if dashscope_key.strip():
                self.processor.set_api_key("dashscope", dashscope_key.strip())

            if openai_key.strip():
                self.processor.set_api_key("openai", openai_key.strip())

            return self._get_api_status_html()

        except Exception as e:
            self.logger.error(f"保存API密钥失败: {str(e)}")
            return f"<p style='color: red;'>保存失败: {str(e)}</p>"

    def refresh_price_models(self):
        """刷新信息价识别模型列表"""
        try:
            # 刷新处理器的模型列表
            success = self.processor.refresh_lm_studio_models()

            # 获取可用模型
            available_models = {}

            # 添加基础模型
            if hasattr(self.processor, 'api_keys') and self.processor.api_keys.get("dashscope"):
                available_models["qwen_qvq_max"] = "阿里通义千问-QVQ-Max"

            # 添加LM Studio模型
            if hasattr(self.processor, 'supported_models'):
                for key, name in self.processor.supported_models.items():
                    if key.startswith("lm_studio_"):
                        available_models[key] = name

            # 转换为选择列表格式
            choices = [(name, key) for key, name in available_models.items()]

            # 获取LM Studio状态
            lm_status = self.processor.get_lm_studio_status()

            # 生成状态HTML
            if lm_status["running"]:
                status_html = f"""
                <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 12px; margin: 8px 0;">
                    <h4 style="color: #155724; margin: 0 0 8px 0;">✅ LM Studio运行正常</h4>
                    <p style="color: #155724; margin: 0; font-size: 0.9em;">
                        • 总模型数: {lm_status["models_count"]} 个<br>
                        • 视觉模型数: {lm_status["vision_models_count"]} 个<br>
                        • 服务地址: {lm_status.get("base_url", "http://127.0.0.1:1234")}
                    </p>
                </div>
                """
            else:
                status_html = f"""
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 12px; margin: 8px 0;">
                    <h4 style="color: #721c24; margin: 0 0 8px 0;">❌ LM Studio未运行</h4>
                    <p style="color: #721c24; margin: 0; font-size: 0.9em;">
                        {lm_status["message"]}<br>
                        💡 请启动LM Studio并加载视觉语言模型
                    </p>
                </div>
                """

            # 选择默认值
            default_value = None
            if choices:
                # 优先选择LM Studio模型
                lm_studio_choices = [choice for choice in choices if choice[1].startswith("lm_studio_")]
                if lm_studio_choices:
                    default_value = lm_studio_choices[0][1]
                else:
                    default_value = choices[0][1]

            return gr.update(choices=choices, value=default_value), status_html

        except Exception as e:
            error_html = f"""
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 12px; margin: 8px 0;">
                <h4 style="color: #721c24; margin: 0 0 8px 0;">❌ 刷新模型失败</h4>
                <p style="color: #721c24; margin: 0; font-size: 0.9em;">
                    错误信息: {str(e)}
                </p>
            </div>
            """
            return gr.update(choices=[("无可用模型", "")], value=""), error_html

    def toggle_db_config(self, db_type: str):
        """根据数据库类型切换配置显示"""
        if db_type in ["mysql", "postgresql"]:
            return gr.update(visible=True)
        else:
            return gr.update(visible=False)

    def write_to_database(self, db_type: str, db_name: str, merge_strategy: str,
                         db_host: str, db_port: int, db_user: str,
                         db_password: str, default_db: str) -> tuple[str, str]:
        """将信息价数据写入数据库"""
        try:
            # 检查是否有可用的CSV数据
            if not hasattr(self.processor, 'last_csv_file') or not self.processor.last_csv_file:
                return (
                    "<p style='color: orange;'>⚠️ 请先完成信息价识别，生成CSV数据后再写入数据库</p>",
                    ""
                )

            csv_file = self.processor.last_csv_file
            if not os.path.exists(csv_file):
                return (
                    "<p style='color: red;'>❌ 找不到CSV数据文件，请重新进行信息价识别</p>",
                    ""
                )

            # 导入MCP数据库转换器
            from .mcp_database_converter import MCPDatabaseConverter
            converter = MCPDatabaseConverter()

            # 根据合并策略确定最终的数据库名称
            if merge_strategy == "timestamp":
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                if '.' in db_name:
                    name_parts = db_name.rsplit('.', 1)
                    final_db_name = f"{name_parts[0]}_{timestamp}.{name_parts[1]}"
                else:
                    final_db_name = f"{db_name}_{timestamp}"
            else:
                final_db_name = db_name

            # 构建数据库配置
            if db_type == "sqlite":
                output_path = os.path.join("output", final_db_name)
                success, message, stats = converter.convert_to_sqlite([csv_file], output_path, merge_strategy)

            elif db_type == "mongodb":
                json_filename = final_db_name if final_db_name.endswith('.json') else f"{final_db_name}.json"
                output_path = os.path.join("output", json_filename)
                success, message, stats = converter.convert_to_mongodb([csv_file], output_path, merge_strategy)

            elif db_type in ["mysql", "postgresql"]:
                # 对于MySQL和PostgreSQL，需要使用企业定额管理器
                from .enterprise_quota_manager import EnterpriseQuotaManager
                manager = EnterpriseQuotaManager()

                db_config = {
                    'host': db_host,
                    'port': int(db_port),
                    'user': db_user,
                    'password': db_password,
                    'database': final_db_name,
                    'default_db': default_db
                }

                success, message = manager.create_quota_database(
                    db_type, db_config, [csv_file], [], merge_strategy
                )
                stats = {}
            else:
                return (
                    "<p style='color: red;'>❌ 不支持的数据库类型</p>",
                    ""
                )

            if success:
                # 构建成功消息
                strategy_messages = {
                    "smart_merge": "🧠 智能合并：信息价数据已与现有数据智能合并",
                    "replace": "🔄 完全覆盖：现有数据已被信息价数据完全替换",
                    "timestamp": f"🕒 时间戳模式：已创建新数据库 {final_db_name}"
                }

                strategy_msg = strategy_messages.get(merge_strategy, "")
                success_message = f"✅ 信息价数据库写入成功！\n📂 数据库名称: {final_db_name}\n{strategy_msg}"

                # 生成统计信息
                if stats:
                    stats_html = f"""
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                        <h4 style="margin-top: 0; color: #333;">📊 数据库统计信息</h4>
                        <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                            <p style="margin: 4px 0;"><strong>📂 数据库信息:</strong></p>
                            <p style="margin: 4px 0;">• 数据库名称: {final_db_name}</p>
                            <p style="margin: 4px 0;">• 数据库类型: {db_type.upper()}</p>
                            <p style="margin: 4px 0;"><strong>📈 数据统计:</strong></p>
                            <p style="margin: 4px 0;">• 信息价记录数: {stats.get('total_records', 0)} 条</p>
                            <p style="margin: 4px 0;">• 数据表数量: {stats.get('table_count', 1)} 个</p>
                            <p style="margin: 4px 0;">• 数据库大小: {stats.get('file_size_kb', 0):.2f} KB</p>
                        </div>
                    </div>
                    """
                else:
                    stats_html = "<div style='color: #666;'>统计信息暂不可用</div>"

                return success_message, stats_html
            else:
                return f"<p style='color: red;'>❌ 数据库写入失败: {message}</p>", ""

        except Exception as e:
            error_msg = f"数据库写入异常: {str(e)}"
            self.logger.error(error_msg)
            return f"<p style='color: red;'>❌ {error_msg}</p>", ""

    def test_model_connection(self, model_type: str) -> str:
        """测试模型连接"""
        try:
            # 显示测试中状态
            testing_html = """
            <div style="padding: 10px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                <p style="margin: 0; color: #856404;">
                    🔄 正在测试模型连接，请稍候...
                </p>
            </div>
            """

            # 异步测试连接
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                success, message = loop.run_until_complete(
                    self.processor.test_model_connection(model_type)
                )
            finally:
                loop.close()

            # 生成结果HTML
            if success:
                result_html = f"""
                <div style="padding: 10px; background: #d4edda; border-radius: 8px; border-left: 4px solid #28a745;">
                    <p style="margin: 0; color: #155724;">
                        {message}
                    </p>
                </div>
                """
            else:
                result_html = f"""
                <div style="padding: 10px; background: #f8d7da; border-radius: 8px; border-left: 4px solid #dc3545;">
                    <p style="margin: 0; color: #721c24;">
                        {message}
                    </p>
                </div>
                """

            return result_html

        except Exception as e:
            error_html = f"""
            <div style="padding: 10px; background: #f8d7da; border-radius: 8px; border-left: 4px solid #dc3545;">
                <p style="margin: 0; color: #721c24;">
                    ❌ 测试异常: {str(e)}
                </p>
            </div>
            """
            return error_html

    def get_stored_price_pdfs(self):
        """获取已存储的信息价PDF列表"""
        try:
            stored_pdfs = self.pdf_storage.get_stored_pdfs("price")
            choices = []
            for pdf_info in stored_pdfs:
                display_name = f"{pdf_info.get('original_name', 'Unknown')} ({pdf_info.get('size_mb', 0)} MB)"
                choices.append((display_name, pdf_info.get('id', '')))
            return gr.update(choices=choices, value=None)
        except Exception as e:
            self.logger.error(f"获取已存储信息价PDF列表失败: {e}")
            return gr.update(choices=[], value=None)

    def show_stored_price_pdf_info(self, pdf_id):
        """显示已存储信息价PDF的详细信息"""
        try:
            if not pdf_id:
                return "<p style='color: #666; text-align: center;'>选择PDF文件查看详情</p>"

            pdf_info = self.pdf_storage.get_pdf_info(pdf_id)
            if not pdf_info:
                return "<p style='color: #f56565; text-align: center;'>文件不存在</p>"

            # 格式化上传时间
            upload_time = pdf_info.get("upload_time", "")
            if upload_time:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(upload_time.replace("Z", "+00:00"))
                    upload_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    pass

            html = f"""
            <div style="background: #f8f9fa; border-radius: 10px; padding: 15px; margin: 10px 0;">
                <h4 style="color: #667eea; margin-top: 0; font-size: 1.1em;">💰 {pdf_info.get('original_name', 'Unknown')}</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                    <div><strong>文件大小:</strong> {pdf_info.get('size_mb', 0)} MB</div>
                    <div><strong>上传时间:</strong> {upload_time}</div>
                </div>
                <div style="margin-top: 10px; font-size: 0.8em; color: #666;">
                    <strong>存储路径:</strong><br>
                    <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px; word-break: break-all;">
                        {pdf_info.get('stored_path', 'N/A')}
                    </code>
                </div>
            </div>
            """
            return html

        except Exception as e:
            return f"<p style='color: #f56565; text-align: center;'>获取文件信息失败: {str(e)}</p>"

    def load_stored_price_pdf(self, pdf_id):
        """加载已存储的信息价PDF文件"""
        try:
            if not pdf_id:
                return (
                    """
                    <div style="text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                        <div style="font-size: 3em; margin-bottom: 20px;">📄</div>
                        <h3 style="color: #667eea; margin-bottom: 10px;">PDF智能浏览器</h3>
                        <p style="color: #666;">选择PDF文件后将显示交互式浏览器</p>
                        <p style="color: #999; font-size: 0.9em;">支持页面导航、缩放查看等功能</p>
                    </div>
                    """,
                    gr.update(value=1),
                    gr.update(value="0"),
                    None  # current_price_pdf_path
                )

            pdf_info = self.pdf_storage.get_pdf_info(pdf_id)
            if not pdf_info:
                error_html = """
                <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                    <h3 style="margin-top: 0; color: #c62828;">❌ 文件不存在</h3>
                    <p style="color: #666;">选择的PDF文件不存在或已被删除</p>
                </div>
                """
                return (
                    error_html,
                    gr.update(value=1),
                    gr.update(value="0"),
                    None
                )

            pdf_path = pdf_info.get('stored_path', '')
            if not os.path.exists(pdf_path):
                error_html = """
                <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                    <h3 style="margin-top: 0; color: #c62828;">❌ 文件路径无效</h3>
                    <p style="color: #666;">PDF文件路径不存在，可能已被移动或删除</p>
                </div>
                """
                return (
                    error_html,
                    gr.update(value=1),
                    gr.update(value="0"),
                    None
                )

            # 使用现有的handle_pdf_upload逻辑
            import fitz  # PyMuPDF

            doc = fitz.open(pdf_path)
            total_pages = len(doc)
            doc.close()

            # 显示第一页
            page_html = self.render_pdf_page(pdf_path, 1)

            self.logger.info(f"💰 已加载存储的信息价PDF: {pdf_info.get('original_name', 'Unknown')}")

            return (
                page_html,
                gr.update(value=1, maximum=total_pages),
                gr.update(value=str(total_pages)),
                pdf_path  # 返回文件路径供后续处理使用
            )

        except Exception as e:
            error_html = f"""
            <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                <h3 style="margin-top: 0; color: #c62828;">❌ PDF加载失败</h3>
                <p style="color: #666;">错误信息: {str(e)}</p>
                <p style="color: #666;">请尝试重新选择文件</p>
            </div>
            """
            return (
                error_html,
                gr.update(value=1),
                gr.update(value="0"),
                None
            )

    def handle_pdf_upload(self, pdf_file):
        """处理PDF文件上传"""
        if not pdf_file:
            return (
                """
                <div style="text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                    <div style="font-size: 3em; margin-bottom: 20px;">📄</div>
                    <h3 style="color: #667eea; margin-bottom: 10px;">PDF智能浏览器</h3>
                    <p style="color: #666;">上传PDF文件后将显示交互式浏览器</p>
                    <p style="color: #999; font-size: 0.9em;">支持页面导航、缩放查看等功能</p>
                </div>
                """,
                gr.update(value=1),
                gr.update(value="0"),
                None  # current_price_pdf_path
            )

        try:
            import fitz  # PyMuPDF

            # 存储PDF文件到系统
            original_name = os.path.basename(pdf_file) if pdf_file else "unknown.pdf"
            success, stored_path, file_info = self.pdf_storage.store_price_pdf(pdf_file, original_name)

            if success:
                if "existing_id" in file_info:
                    self.logger.info(f"📄 信息价PDF文件已存在: {original_name} (ID: {file_info['existing_id']})")
                else:
                    self.logger.info(f"📄 信息价PDF文件已保存: {original_name} -> {file_info['id']}")
                    self.logger.info(f"   存储路径: {stored_path}")
                    self.logger.info(f"   文件大小: {file_info['size_mb']} MB")
            else:
                self.logger.warning(f"⚠️ 信息价PDF文件存储失败: {file_info.get('error', '未知错误')}")

            doc = fitz.open(pdf_file)
            total_pages = len(doc)
            doc.close()

            # 显示第一页
            page_html = self.render_pdf_page(pdf_file, 1)

            return (
                page_html,
                gr.update(value=1, maximum=total_pages),
                gr.update(value=str(total_pages)),
                pdf_file  # current_price_pdf_path
            )
        except Exception as e:
            error_html = f"""
            <div style="text-align: center; padding: 40px; background: #fff3cd; border-radius: 15px; border: 2px solid #ffc107;">
                <div style="font-size: 2.5em; margin-bottom: 15px;">⚠️</div>
                <h3 style="color: #856404; margin-bottom: 10px;">PDF加载失败</h3>
                <p style="color: #856404;">错误信息: {str(e)}</p>
                <p style="color: #6c757d; font-size: 0.9em;">请确保上传的是有效的PDF文件</p>
            </div>
            """
            return (
                error_html,
                gr.update(value=1),
                gr.update(value="0"),
                None  # current_price_pdf_path
            )

    def render_pdf_page(self, pdf_file, page_num, zoom_level=2.0):
        """渲染指定页面"""
        try:
            import fitz  # PyMuPDF
            import base64

            doc = fitz.open(pdf_file)
            page = doc[page_num - 1]  # 页面索引从0开始

            # 设置缩放矩阵
            mat = fitz.Matrix(zoom_level, zoom_level)
            pix = page.get_pixmap(matrix=mat)

            # 转换为PNG字节
            img_data = pix.tobytes("png")

            # 转换为base64
            img_base64 = base64.b64encode(img_data).decode()

            doc.close()

            # 生成HTML
            html = f"""
            <div style="text-align: center; padding: 20px; background: white; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                <div style="margin-bottom: 15px;">
                    <span style="background: #667eea; color: white; padding: 8px 16px; border-radius: 20px; font-size: 0.9em;">
                        📄 第 {page_num} 页
                    </span>
                </div>
                <div style="border: 2px solid #e9ecef; border-radius: 10px; overflow: hidden; display: inline-block; max-width: 100%;">
                    <img src="data:image/png;base64,{img_base64}"
                         style="max-width: 100%; height: auto; display: block;"
                         alt="PDF页面 {page_num}"/>
                </div>
                <div style="margin-top: 15px; color: #6c757d; font-size: 0.9em;">
                    💡 使用上方按钮进行页面导航和缩放操作
                </div>
            </div>
            """

            return html

        except Exception as e:
            return f"""
            <div style="text-align: center; padding: 40px; background: #f8d7da; border-radius: 15px; border: 2px solid #dc3545;">
                <div style="font-size: 2.5em; margin-bottom: 15px;">❌</div>
                <h3 style="color: #721c24; margin-bottom: 10px;">页面渲染失败</h3>
                <p style="color: #721c24;">错误信息: {str(e)}</p>
            </div>
            """

    def show_page(self, current_price_pdf_path, page_num):
        """显示指定页面"""
        if not current_price_pdf_path:
            return "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

        return self.render_pdf_page(current_price_pdf_path, int(page_num))

    def prev_page(self, current_price_pdf_path, current_page):
        """上一页"""
        if not current_price_pdf_path:
            return current_page, "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

        new_page = max(1, current_page - 1)
        page_html = self.render_pdf_page(current_price_pdf_path, new_page)
        return new_page, page_html

    def next_page(self, current_price_pdf_path, current_page, total_pages):
        """下一页"""
        if not current_price_pdf_path:
            return current_page, "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

        max_pages = int(total_pages) if total_pages.isdigit() else 1
        new_page = min(max_pages, current_page + 1)
        page_html = self.render_pdf_page(current_price_pdf_path, new_page)
        return new_page, page_html

    def zoom_page(self, current_price_pdf_path, current_page):
        """放大查看当前页面"""
        if not current_price_pdf_path:
            return "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

        # 使用更高的缩放级别
        return self.render_pdf_page(current_price_pdf_path, current_page, zoom_level=3.0)

    def process_price_info(
        self,
        pdf_path: str,
        start_page: int,
        end_page: int,
        model_type: str
    ) -> Tuple[str, str, pd.DataFrame, str, str, str]:
        """处理信息价识别"""
        try:
            if not pdf_path:
                return (
                    "<p style='color: red;'>❌ 请先上传PDF文件</p>",
                    "<p style='color: #666;'>暂无数据</p>",
                    pd.DataFrame(),
                    gr.update(visible=False),  # CSV文件
                    gr.update(visible=False),  # JSON文件
                    ""     # 下载状态
                )

            # 显示处理状态
            status_html = "<p style='color: blue;'>🔄 正在处理信息价识别...</p>"

            # 异步处理
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                success, message, stats = loop.run_until_complete(
                    self.processor.process_price_info_pdf(
                        pdf_path, start_page, end_page, model_type
                    )
                )
            finally:
                loop.close()

            if success:
                # 生成统计信息HTML
                stats_html = f"""
                <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                    <h4 style="color: #0ea5e9; margin: 0 0 10px 0;">📊 处理统计</h4>
                    <p><strong>总页数:</strong> {stats.get('total_pages', 0)}</p>
                    <p><strong>成功页数:</strong> {stats.get('processed_pages', 0)}</p>
                    <p><strong>识别章节:</strong> {stats.get('total_chapters', 0)}</p>
                    <p><strong>价格条目:</strong> {stats.get('total_price_items', 0)}</p>
                </div>
                """

                # 加载预览数据和准备下载文件
                output_files = stats.get('output_files', [])
                preview_df = pd.DataFrame()
                csv_file_path = None
                json_file_path = None
                download_html = ""

                if output_files:
                    # 分离CSV和JSON文件
                    csv_files = [f for f in output_files if f.endswith('.csv')]
                    json_files = [f for f in output_files if f.endswith('.json')]

                    # 设置下载文件路径
                    if csv_files:
                        csv_file_path = csv_files[0]
                        preview_df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
                        # 只显示前10行
                        if len(preview_df) > 10:
                            preview_df = preview_df.head(10)

                    if json_files:
                        json_file_path = json_files[0]

                    # 生成下载状态信息
                    download_html = f"""
                    <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; border-left: 4px solid #22c55e;">
                        <h4 style="color: #22c55e; margin: 0 0 10px 0;">📁 文件已生成</h4>
                        <p style="color: #666; font-size: 0.9em;">
                            ✅ 生成了 {len(output_files)} 个文件，请使用下方的下载按钮获取文件
                        </p>
                        <ul style="color: #666; font-size: 0.9em; margin: 5px 0;">
                            {''.join([f'<li>{os.path.basename(f)}</li>' for f in output_files])}
                        </ul>
                    </div>
                    """

                return (
                    f"<p style='color: green;'>✅ {message}</p>",
                    stats_html,
                    preview_df,
                    gr.update(value=csv_file_path, visible=True) if csv_file_path else gr.update(visible=False),   # CSV文件
                    gr.update(value=json_file_path, visible=True) if json_file_path else gr.update(visible=False),  # JSON文件
                    download_html    # 下载状态
                )
            else:
                return (
                    f"<p style='color: red;'>❌ {message}</p>",
                    "<p style='color: #666;'>处理失败</p>",
                    pd.DataFrame(),
                    gr.update(visible=False),  # CSV文件
                    gr.update(visible=False),  # JSON文件
                    ""     # 下载状态
                )

        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            self.logger.error(error_msg)
            return (
                f"<p style='color: red;'>❌ {error_msg}</p>",
                "<p style='color: #666;'>处理失败</p>",
                pd.DataFrame(),
                gr.update(visible=False),  # CSV文件
                gr.update(visible=False),  # JSON文件
                ""     # 下载状态
            )
