#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
定额修订处理器
负责定额数据的加载、编辑、保存和PDF预览功能
"""

import os
import sqlite3
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import fitz  # PyMuPDF
from PIL import Image
import tempfile
import logging

class QuotaRevisionProcessor:
    """定额修订处理器"""
    
    def __init__(self, logger=None, quota_manager=None):
        """初始化定额修订处理器"""
        self.logger = logger or logging.getLogger(__name__)
        self.quota_manager = quota_manager  # 关联高级定额管理器
        self.current_db_path = None
        self.current_connection = None
        self.stored_pdfs_dir = Path("stored_pdfs")
        self.temp_dir = Path("temp")

        # 确保目录存在
        self.stored_pdfs_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)

        self.logger.info("定额修订处理器初始化完成")

    def load_from_connected_database(self) -> Tuple[bool, str, List[List[str]], Dict[str, int]]:
        """从已连接的数据库加载定额数据"""
        try:
            if not self.quota_manager or not self.quota_manager.connection:
                return False, "❌ 未连接数据库，请先在数据库连接页面连接数据库", [], {}

            # 使用已连接的数据库
            self.current_connection = self.quota_manager.connection
            self.current_db_path = self.quota_manager.db_path

            # 获取定额和资源数据
            quota_data, stats = self._load_quota_resource_data()

            self.logger.info(f"✅ 从已连接数据库加载数据成功")
            self.logger.info(f"📊 统计: {stats}")

            return True, f"✅ 成功加载数据", quota_data, stats

        except Exception as e:
            self.logger.error(f"❌ 从连接数据库加载数据失败: {str(e)}")
            return False, f"❌ 加载数据失败: {str(e)}", [], {}

    def _load_quota_resource_data(self) -> Tuple[List[List[str]], Dict[str, int]]:
        """加载定额和资源数据，按Excel风格组织"""
        try:
            if not self.current_connection:
                return [], {}

            cursor = self.current_connection.cursor()

            # 获取所有定额项，按定额编码排序
            cursor.execute("""
                SELECT quota_code, quota_name, unit, work_content
                FROM parent_quotas
                ORDER BY quota_code
            """)

            quotas = cursor.fetchall()
            quota_data = []
            quota_count = 0
            resource_count = 0

            for quota in quotas:
                quota_code, quota_name, unit, work_content = quota
                quota_count += 1

                # 获取该定额的所有资源
                cursor.execute("""
                    SELECT resource_code, resource_name, category, unit, consumption
                    FROM child_resources
                    WHERE quota_code = ?
                    ORDER BY resource_code
                """, (quota_code,))

                resources = cursor.fetchall()

                if resources:
                    # 第一行显示定额信息和第一个资源
                    first_resource = resources[0]
                    quota_data.append([
                        "定额项",  # 类型
                        quota_code or '',  # 定额编码
                        quota_name or '',  # 定额名称
                        unit or '',  # 单位
                        work_content or '',  # 工作内容
                        first_resource[0] or '',  # 资源编码
                        first_resource[1] or '',  # 资源名称
                        first_resource[2] or '',  # 资源类别
                        first_resource[3] or '',  # 资源单位
                        str(first_resource[4]) if first_resource[4] is not None else ''  # 消耗量
                    ])
                    resource_count += 1

                    # 后续行只显示资源信息
                    for resource in resources[1:]:
                        quota_data.append([
                            "资源项",  # 类型
                            quota_code,  # 定额编码（用于关联）
                            '',  # 定额名称（空）
                            '',  # 单位（空）
                            '',  # 工作内容（空）
                            resource[0] or '',  # 资源编码
                            resource[1] or '',  # 资源名称
                            resource[2] or '',  # 资源类别
                            resource[3] or '',  # 资源单位
                            str(resource[4]) if resource[4] is not None else ''  # 消耗量
                        ])
                        resource_count += 1
                else:
                    # 没有资源的定额项
                    quota_data.append([
                        "定额项",  # 类型
                        quota_code or '',  # 定额编码
                        quota_name or '',  # 定额名称
                        unit or '',  # 单位
                        work_content or '',  # 工作内容
                        '',  # 资源编码（空）
                        '',  # 资源名称（空）
                        '',  # 资源类别（空）
                        '',  # 资源单位（空）
                        ''   # 消耗量（空）
                    ])

            stats = {
                'quota_count': quota_count,
                'resource_count': resource_count,
                'total_rows': len(quota_data)
            }

            self.logger.info(f"📊 加载数据统计: {quota_count}个定额项, {resource_count}个资源项, 共{len(quota_data)}行")

            return quota_data, stats

        except Exception as e:
            self.logger.error(f"加载定额资源数据失败: {str(e)}")
            return [], {}

    def load_database(self, db_path: str) -> Tuple[bool, str, List[str]]:
        """加载定额数据库"""
        try:
            if not os.path.exists(db_path):
                return False, f"数据库文件不存在: {db_path}", []
            
            # 关闭之前的连接
            if self.current_connection:
                self.current_connection.close()
            
            # 连接新数据库
            self.current_connection = sqlite3.connect(db_path)
            self.current_db_path = db_path
            
            # 获取定额项列表
            quota_list = self._get_quota_list()
            
            self.logger.info(f"✅ 成功加载数据库: {db_path}")
            self.logger.info(f"📊 发现 {len(quota_list)} 个定额项")
            
            return True, f"✅ 成功加载数据库，发现 {len(quota_list)} 个定额项", quota_list
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据库失败: {str(e)}")
            return False, f"❌ 加载数据库失败: {str(e)}", []
    
    def _get_quota_list(self) -> List[str]:
        """获取定额项列表"""
        try:
            if not self.current_connection:
                return []
            
            cursor = self.current_connection.cursor()
            
            # 查询定额项
            cursor.execute("""
                SELECT DISTINCT quota_code, quota_name 
                FROM parent_quotas 
                ORDER BY quota_code
            """)
            
            results = cursor.fetchall()
            quota_list = [f"{code} - {name}" for code, name in results]
            
            return quota_list
            
        except Exception as e:
            self.logger.error(f"获取定额项列表失败: {str(e)}")
            return []
    
    def get_quota_details(self, quota_selection: str) -> Tuple[Dict[str, str], List[List[str]]]:
        """获取定额项详细信息和关联资源"""
        try:
            if not self.current_connection or not quota_selection:
                return {}, []
            
            # 解析选择的定额项
            quota_code = quota_selection.split(" - ")[0]
            
            cursor = self.current_connection.cursor()
            
            # 获取定额项基本信息
            cursor.execute("""
                SELECT quota_code, quota_name, unit, work_content
                FROM parent_quotas 
                WHERE quota_code = ?
                LIMIT 1
            """, (quota_code,))
            
            quota_result = cursor.fetchone()
            if not quota_result:
                return {}, []
            
            quota_info = {
                'code': quota_result[0] or '',
                'name': quota_result[1] or '',
                'unit': quota_result[2] or '',
                'work_content': quota_result[3] or ''
            }
            
            # 获取关联资源
            cursor.execute("""
                SELECT resource_code, resource_name, category, unit, consumption
                FROM child_resources 
                WHERE quota_code = ?
                ORDER BY resource_code
            """, (quota_code,))
            
            resource_results = cursor.fetchall()
            resources_data = []
            
            for resource in resource_results:
                resources_data.append([
                    resource[0] or '',  # 资源编码
                    resource[1] or '',  # 资源名称
                    resource[2] or '',  # 类别
                    resource[3] or '',  # 单位
                    str(resource[4]) if resource[4] is not None else ''  # 消耗量
                ])
            
            self.logger.info(f"📋 获取定额 {quota_code} 详情: {len(resources_data)} 个资源项")
            
            return quota_info, resources_data
            
        except Exception as e:
            self.logger.error(f"获取定额详情失败: {str(e)}")
            return {}, []
    
    def save_quota_info(self, quota_code: str, quota_name: str, unit: str, work_content: str) -> Tuple[bool, str]:
        """保存定额项基本信息"""
        try:
            if not self.current_connection:
                return False, "❌ 未连接数据库"
            
            cursor = self.current_connection.cursor()
            
            # 更新定额项信息
            cursor.execute("""
                UPDATE parent_quotas 
                SET quota_name = ?, unit = ?, work_content = ?
                WHERE quota_code = ?
            """, (quota_name, unit, work_content, quota_code))
            
            self.current_connection.commit()
            
            self.logger.info(f"💾 保存定额项 {quota_code} 信息成功")
            return True, f"✅ 定额项 {quota_code} 信息已保存"
            
        except Exception as e:
            self.logger.error(f"保存定额项信息失败: {str(e)}")
            return False, f"❌ 保存失败: {str(e)}"
    
    def save_resources_data(self, quota_code: str, resources_data: List[List[str]]) -> Tuple[bool, str]:
        """保存资源数据"""
        try:
            if not self.current_connection:
                return False, "❌ 未连接数据库"
            
            cursor = self.current_connection.cursor()
            
            # 删除原有资源数据
            cursor.execute("DELETE FROM child_resources WHERE quota_code = ?", (quota_code,))
            
            # 插入新的资源数据
            for resource in resources_data:
                if len(resource) >= 5 and resource[0]:  # 确保有资源编码
                    cursor.execute("""
                        INSERT INTO child_resources 
                        (quota_code, resource_code, resource_name, category, unit, consumption)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        quota_code,
                        resource[0],  # 资源编码
                        resource[1],  # 资源名称
                        resource[2],  # 类别
                        resource[3],  # 单位
                        float(resource[4]) if resource[4] and resource[4] != '' else 0.0  # 消耗量
                    ))
            
            self.current_connection.commit()
            
            self.logger.info(f"💾 保存定额 {quota_code} 的 {len(resources_data)} 个资源项")
            return True, f"✅ 已保存 {len(resources_data)} 个资源项"
            
        except Exception as e:
            self.logger.error(f"保存资源数据失败: {str(e)}")
            return False, f"❌ 保存资源失败: {str(e)}"
    
    def delete_quota(self, quota_code: str) -> Tuple[bool, str]:
        """删除定额项及其关联资源"""
        try:
            if not self.current_connection:
                return False, "❌ 未连接数据库"
            
            cursor = self.current_connection.cursor()
            
            # 删除关联资源
            cursor.execute("DELETE FROM child_resources WHERE quota_code = ?", (quota_code,))
            
            # 删除定额项
            cursor.execute("DELETE FROM parent_quotas WHERE quota_code = ?", (quota_code,))
            
            self.current_connection.commit()
            
            self.logger.info(f"🗑️ 删除定额项 {quota_code} 及其关联资源")
            return True, f"✅ 已删除定额项 {quota_code}"
            
        except Exception as e:
            self.logger.error(f"删除定额项失败: {str(e)}")
            return False, f"❌ 删除失败: {str(e)}"
    
    def get_available_pdfs(self) -> List[str]:
        """获取可用的PDF文件列表"""
        try:
            pdf_files = []

            # 检查stored_pdfs目录
            if self.stored_pdfs_dir.exists():
                self.logger.info(f"检查目录: {self.stored_pdfs_dir}")
                for pdf_file in self.stored_pdfs_dir.rglob("*.pdf"):
                    relative_path = str(pdf_file.relative_to(self.stored_pdfs_dir))
                    pdf_files.append(f"stored_pdfs/{relative_path}")
                    self.logger.info(f"找到PDF: {relative_path}")

            # 检查根目录的PDF文件
            root_dir = Path(".")
            for pdf_file in root_dir.glob("*.pdf"):
                pdf_files.append(str(pdf_file))
                self.logger.info(f"找到根目录PDF: {pdf_file}")

            # 检查output目录的PDF文件
            output_dir = Path("output")
            if output_dir.exists():
                for pdf_file in output_dir.rglob("*.pdf"):
                    relative_path = str(pdf_file.relative_to(Path(".")))
                    pdf_files.append(relative_path)
                    self.logger.info(f"找到输出目录PDF: {relative_path}")

            self.logger.info(f"📁 总共发现 {len(pdf_files)} 个PDF文件: {pdf_files}")
            return sorted(pdf_files)

        except Exception as e:
            self.logger.error(f"获取PDF文件列表失败: {str(e)}")
            return []
    
    def render_pdf_page(self, pdf_path: str, page_num: int) -> Tuple[bool, str, Optional[str]]:
        """渲染PDF页面为图片"""
        try:
            # 确定完整的PDF路径
            if not os.path.isabs(pdf_path):
                # 尝试在stored_pdfs目录中查找
                stored_pdf_path = self.stored_pdfs_dir / pdf_path
                if stored_pdf_path.exists():
                    full_pdf_path = str(stored_pdf_path)
                else:
                    # 尝试在根目录查找
                    root_pdf_path = Path(pdf_path)
                    if root_pdf_path.exists():
                        full_pdf_path = str(root_pdf_path)
                    else:
                        return False, f"❌ PDF文件不存在: {pdf_path}", None
            else:
                full_pdf_path = pdf_path
            
            if not os.path.exists(full_pdf_path):
                return False, f"❌ PDF文件不存在: {full_pdf_path}", None
            
            # 打开PDF文档
            doc = fitz.open(full_pdf_path)
            
            if page_num < 1 or page_num > len(doc):
                doc.close()
                return False, f"❌ 页码超出范围 (1-{len(doc)})", None
            
            # 获取页面
            page = doc[page_num - 1]  # PyMuPDF使用0基索引
            
            # 渲染页面为图片
            mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放以提高清晰度
            pix = page.get_pixmap(matrix=mat)
            
            # 保存为临时图片文件
            temp_image_path = self.temp_dir / f"pdf_page_{page_num}_{hash(full_pdf_path)}.png"
            pix.save(str(temp_image_path))
            
            doc.close()
            
            self.logger.info(f"📄 渲染PDF页面: {pdf_path} 第{page_num}页")
            return True, f"✅ 已渲染第{page_num}页", str(temp_image_path)
            
        except Exception as e:
            self.logger.error(f"渲染PDF页面失败: {str(e)}")
            return False, f"❌ 渲染失败: {str(e)}", None
    
    def get_pdf_page_count(self, pdf_path: str) -> int:
        """获取PDF总页数"""
        try:
            # 确定完整的PDF路径
            if not os.path.isabs(pdf_path):
                stored_pdf_path = self.stored_pdfs_dir / pdf_path
                if stored_pdf_path.exists():
                    full_pdf_path = str(stored_pdf_path)
                else:
                    root_pdf_path = Path(pdf_path)
                    if root_pdf_path.exists():
                        full_pdf_path = str(root_pdf_path)
                    else:
                        return 0
            else:
                full_pdf_path = pdf_path
            
            if not os.path.exists(full_pdf_path):
                return 0
            
            doc = fitz.open(full_pdf_path)
            page_count = len(doc)
            doc.close()
            
            return page_count
            
        except Exception as e:
            self.logger.error(f"获取PDF页数失败: {str(e)}")
            return 0
    
    def save_excel_data(self, table_data: List[List[str]]) -> Tuple[bool, str]:
        """保存Excel风格的表格数据到数据库"""
        try:
            if not self.current_connection:
                return False, "❌ 未连接数据库"

            cursor = self.current_connection.cursor()

            # 清空现有数据
            cursor.execute("DELETE FROM child_resources")
            cursor.execute("DELETE FROM parent_quotas")

            # 按定额编码分组处理数据
            quota_dict = {}

            for row in table_data:
                if len(row) < 10:
                    continue

                row_type, quota_code, quota_name, unit, work_content, resource_code, resource_name, resource_category, resource_unit, consumption = row

                if not quota_code:
                    continue

                # 处理定额项
                if row_type == "定额项" and quota_name:
                    quota_dict[quota_code] = {
                        'name': quota_name,
                        'unit': unit,
                        'work_content': work_content,
                        'resources': []
                    }

                # 处理资源项
                if resource_code and resource_name:
                    if quota_code not in quota_dict:
                        quota_dict[quota_code] = {
                            'name': quota_name or '',
                            'unit': unit or '',
                            'work_content': work_content or '',
                            'resources': []
                        }

                    quota_dict[quota_code]['resources'].append({
                        'code': resource_code,
                        'name': resource_name,
                        'category': resource_category,
                        'unit': resource_unit,
                        'consumption': float(consumption) if consumption and consumption != '' else 0.0
                    })

            # 插入定额数据
            for quota_code, quota_info in quota_dict.items():
                cursor.execute("""
                    INSERT INTO parent_quotas (quota_code, quota_name, unit, work_content)
                    VALUES (?, ?, ?, ?)
                """, (quota_code, quota_info['name'], quota_info['unit'], quota_info['work_content']))

                # 插入资源数据
                for resource in quota_info['resources']:
                    cursor.execute("""
                        INSERT INTO child_resources
                        (quota_code, resource_code, resource_name, category, unit, consumption)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        quota_code,
                        resource['code'],
                        resource['name'],
                        resource['category'],
                        resource['unit'],
                        resource['consumption']
                    ))

            self.current_connection.commit()

            self.logger.info(f"💾 保存Excel数据成功: {len(quota_dict)} 个定额项")
            return True, f"✅ 成功保存 {len(quota_dict)} 个定额项"

        except Exception as e:
            self.logger.error(f"保存Excel数据失败: {str(e)}")
            return False, f"❌ 保存失败: {str(e)}"

    def close_database(self):
        """关闭数据库连接"""
        if self.current_connection:
            self.current_connection.close()
            self.current_connection = None
            self.current_db_path = None
            self.logger.info("🔒 数据库连接已关闭")
