#!/usr/bin/env python3
"""
定额创建工具界面组件
Quota Creation Tool Interface Components
"""

import gradio as gr
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional

try:
    from .advanced_quota_manager import AdvancedQuotaManager
    from .price_info_interface import PriceInfoInterface
except ImportError:
    try:
        from advanced_quota_manager import AdvancedQuotaManager
        from price_info_interface import PriceInfoInterface
    except ImportError:
        print("警告: 无法导入AdvancedQuotaManager或PriceInfoInterface")
        AdvancedQuotaManager = None
        PriceInfoInterface = None

class AdvancedQuotaInterface:
    """定额创建工具界面类"""
    
    def __init__(self):
        if AdvancedQuotaManager:
            self.manager = AdvancedQuotaManager()
        else:
            self.manager = None

        if PriceInfoInterface:
            self.price_info_interface = PriceInfoInterface()
        else:
            self.price_info_interface = None

        self.current_table = None
        self.current_page = 0
        self.page_size = 50

    def create_database_connection_interface(self):
        """创建数据库连接界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">🔗</span>高级数据库连接
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    支持多种数据库类型的连接和管理
                </p>
            """)
            
            # 数据库类型选择
            db_type_dropdown = gr.Dropdown(
                label="🗄️ 数据库类型",
                choices=[
                    ("📱 SQLite本地数据库", "sqlite"),
                    ("🐘 PostgreSQL数据库", "postgresql"),
                    ("🐬 MySQL数据库", "mysql"),
                    ("🍃 MongoDB数据库", "mongodb"),
                    ("🏢 SQL Server数据库", "sql_server"),
                    ("🔶 Oracle数据库", "oracle")
                ],
                value="sqlite",
                info="选择要连接的数据库类型",
                interactive=True,
                elem_id="advanced_db_type_dropdown"
            )
            
            # 动态连接配置区域
            with gr.Group() as connection_config:
                # SQLite配置
                with gr.Group(visible=True) as sqlite_config:
                    db_path_input = gr.Textbox(
                        label="📂 数据库文件路径",
                        placeholder="output/enterprise_quota.db",
                        value="output/enterprise_quota.db",
                        info="SQLite数据库文件的完整路径"
                    )
                
                # 其他数据库配置
                with gr.Group(visible=False) as other_db_config:
                    with gr.Row():
                        db_host_input = gr.Textbox(
                            label="🌐 主机地址",
                            placeholder="localhost",
                            value="localhost"
                        )
                        db_port_input = gr.Number(
                            label="🔌 端口号",
                            value=5432,
                            precision=0
                        )
                    
                    with gr.Row():
                        db_name_input = gr.Textbox(
                            label="🗄️ 数据库名",
                            placeholder="enterprise_quota"
                        )
                        db_user_input = gr.Textbox(
                            label="👤 用户名",
                            placeholder="postgres"
                        )
                    
                    db_password_input = gr.Textbox(
                        label="🔐 密码",
                        type="password",
                        placeholder="请输入密码"
                    )
            
            # 连接操作按钮
            with gr.Row():
                test_connection_btn = gr.Button("🧪 测试连接", elem_classes="btn-secondary")
                connect_db_btn = gr.Button("🔌 连接数据库", elem_classes="btn-primary")
                disconnect_db_btn = gr.Button("🔌 断开连接", elem_classes="btn-danger", visible=False)
            
            # 连接状态显示
            connection_status = gr.HTML(
                value="<p style='color: #666;'>等待连接数据库...</p>",
                label="连接状态"
            )
        
        return {
            'db_type_dropdown': db_type_dropdown,
            'sqlite_config': sqlite_config,
            'other_db_config': other_db_config,
            'db_path_input': db_path_input,
            'db_host_input': db_host_input,
            'db_port_input': db_port_input,
            'db_name_input': db_name_input,
            'db_user_input': db_user_input,
            'db_password_input': db_password_input,
            'test_connection_btn': test_connection_btn,
            'connect_db_btn': connect_db_btn,
            'disconnect_db_btn': disconnect_db_btn,
            'connection_status': connection_status
        }

    def create_quota_revision_interface(self):
        """创建定额核对修订界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">📝</span>识别定额修订
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    对比PDF原文，核对和修订AI识别的定额数据，支持实时增删查改
                </p>
            """)

            # 数据库选择和加载
            with gr.Row():
                with gr.Column(scale=2):
                    revision_db_path = gr.Textbox(
                        label="📂 定额数据库路径",
                        placeholder="选择要修订的定额数据库文件...",
                        info="支持SQLite数据库文件"
                    )
                with gr.Column(scale=1):
                    load_revision_db_btn = gr.Button("📥 加载数据库", elem_classes="btn-primary")
                    refresh_revision_data_btn = gr.Button("🔄 刷新数据", elem_classes="btn-secondary")

            # 主要内容区域
            with gr.Row():
                # 左侧：定额数据编辑区
                with gr.Column(scale=1):
                    gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>📋 定额数据编辑</h4>")

                    # 定额项选择
                    quota_selector = gr.Dropdown(
                        label="🎯 选择定额项",
                        choices=[],
                        info="选择要修订的定额项",
                        interactive=True
                    )

                    # 定额项基本信息编辑
                    with gr.Group():
                        gr.HTML("<h5 style='color: #555; margin: 10px 0;'>📝 定额项信息</h5>")
                        quota_code_edit = gr.Textbox(label="定额编码", interactive=True)
                        quota_name_edit = gr.Textbox(label="定额名称", interactive=True)
                        quota_unit_edit = gr.Textbox(label="计量单位", interactive=True)
                        quota_work_content_edit = gr.Textbox(
                            label="工作内容",
                            lines=3,
                            interactive=True
                        )

                        # 定额项操作按钮
                        with gr.Row():
                            save_quota_btn = gr.Button("💾 保存定额", elem_classes="btn-success", scale=1)
                            delete_quota_btn = gr.Button("🗑️ 删除定额", elem_classes="btn-danger", scale=1)

                    # 资源项编辑区
                    with gr.Group():
                        gr.HTML("<h5 style='color: #555; margin: 10px 0;'>🔧 关联资源项</h5>")

                        # 资源项列表
                        resources_dataframe = gr.Dataframe(
                            label="资源消耗清单",
                            headers=["资源编码", "资源名称", "类别", "单位", "消耗量"],
                            datatype=["str", "str", "str", "str", "number"],
                            interactive=True,
                            wrap=True,
                            row_count=(1, "dynamic")
                        )

                        # 资源项操作按钮
                        with gr.Row():
                            add_resource_btn = gr.Button("➕ 添加资源", elem_classes="btn-success", scale=1)
                            delete_resource_btn = gr.Button("➖ 删除资源", elem_classes="btn-warning", scale=1)
                            save_resources_btn = gr.Button("💾 保存资源", elem_classes="btn-primary", scale=1)

                # 右侧：PDF预览区
                with gr.Column(scale=1):
                    gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>📄 PDF原文对比</h4>")

                    # PDF文件选择
                    with gr.Row():
                        pdf_file_selector = gr.Dropdown(
                            label="📁 选择PDF文件",
                            choices=[],
                            info="选择要对比的PDF文件",
                            scale=2
                        )
                        refresh_pdf_list_btn = gr.Button("🔄 刷新", elem_classes="btn-secondary", scale=1)

                    # PDF预览控制
                    with gr.Row():
                        pdf_page_input = gr.Number(
                            label="页码",
                            value=1,
                            minimum=1,
                            precision=0,
                            scale=1
                        )
                        prev_pdf_page_btn = gr.Button("⬅️", elem_classes="btn-secondary", scale=1)
                        next_pdf_page_btn = gr.Button("➡️", elem_classes="btn-secondary", scale=1)
                        zoom_pdf_btn = gr.Button("🔍 缩放", elem_classes="btn-secondary", scale=1)

                    # PDF显示区域
                    pdf_preview = gr.Image(
                        label="PDF预览",
                        type="filepath",
                        interactive=False,
                        height=600
                    )

            # 状态信息和操作日志
            with gr.Row():
                revision_status = gr.HTML(
                    value="<p style='color: #666;'>请先加载定额数据库</p>",
                    label="操作状态"
                )
                revision_log = gr.Textbox(
                    label="📝 操作日志",
                    lines=3,
                    interactive=False,
                    placeholder="操作记录将显示在这里..."
                )

        return {
            'revision_db_path': revision_db_path,
            'load_revision_db_btn': load_revision_db_btn,
            'refresh_revision_data_btn': refresh_revision_data_btn,
            'quota_selector': quota_selector,
            'quota_code_edit': quota_code_edit,
            'quota_name_edit': quota_name_edit,
            'quota_unit_edit': quota_unit_edit,
            'quota_work_content_edit': quota_work_content_edit,
            'save_quota_btn': save_quota_btn,
            'delete_quota_btn': delete_quota_btn,
            'resources_dataframe': resources_dataframe,
            'add_resource_btn': add_resource_btn,
            'delete_resource_btn': delete_resource_btn,
            'save_resources_btn': save_resources_btn,
            'pdf_file_selector': pdf_file_selector,
            'refresh_pdf_list_btn': refresh_pdf_list_btn,
            'pdf_page_input': pdf_page_input,
            'prev_pdf_page_btn': prev_pdf_page_btn,
            'next_pdf_page_btn': next_pdf_page_btn,
            'zoom_pdf_btn': zoom_pdf_btn,
            'pdf_preview': pdf_preview,
            'revision_status': revision_status,
            'revision_log': revision_log
        }

    def create_quota_search_interface(self):
        """创建定额搜索界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">🔍</span>智能定额搜索
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    高级定额搜索，支持关联资源查看和价格分析
                </p>
            """)
            
            # 搜索区域
            with gr.Row():
                search_input = gr.Textbox(
                    label="🔎 搜索关键词",
                    placeholder="输入定额编号、名称或工作内容...",
                    scale=3,
                    info="支持模糊搜索"
                )
                search_btn = gr.Button("🔍 搜索", elem_classes="btn-primary", scale=1)
            
            # 搜索结果
            quota_search_results = gr.Dataframe(
                label="📋 定额项搜索结果",
                interactive=True,
                visible=False,
                wrap=True
            )
            
            # 关联资源显示
            with gr.Row():
                with gr.Column(scale=1):
                    selected_quota_info = gr.HTML(
                        value="<p style='color: #666;'>请选择一个定额项查看详情</p>",
                        label="定额项详情"
                    )
                
                with gr.Column(scale=2):
                    related_resources_display = gr.Dataframe(
                        label="🔧 关联资源",
                        interactive=False,
                        visible=False,
                        wrap=True
                    )
        
        return {
            'search_input': search_input,
            'search_btn': search_btn,
            'quota_search_results': quota_search_results,
            'selected_quota_info': selected_quota_info,
            'related_resources_display': related_resources_display
        }

    def create_resource_price_interface(self):
        """创建资源价格管理界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">💰</span>资源价格管理
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    智能资源价格填报，自动更新定额项总价
                </p>
            """)
            
            # 资源列表
            unique_resources_display = gr.Dataframe(
                label="📊 唯一资源列表",
                headers=["资源编号", "资源名称", "类别", "单位", "平均单价", "最低单价", "最高单价", "使用次数"],
                interactive=True,
                visible=False,
                wrap=True
            )
            
            # 价格更新区域
            with gr.Row():
                with gr.Column(scale=2):
                    selected_resource_info = gr.HTML(
                        value="<p style='color: #666;'>请选择一个资源项进行价格更新</p>",
                        label="选中资源信息"
                    )
                
                with gr.Column(scale=1):
                    new_price_input = gr.Number(
                        label="💰 新单价",
                        minimum=0,
                        step=0.01,
                        info="输入资源的新单价"
                    )
                    
                    update_price_btn = gr.Button("💰 更新价格", elem_classes="btn-warning")
                    recalculate_btn = gr.Button("🔄 重新计算定额价格", elem_classes="btn-success")
            
            # 操作按钮
            with gr.Row():
                load_resources_btn = gr.Button("📊 加载资源列表", elem_classes="btn-primary")
                export_resources_btn = gr.Button("📤 导出资源列表", elem_classes="btn-info")

            # 信息价载入功能区域
            gr.HTML("<hr style='margin: 30px 0;'>")

            with gr.Group(elem_classes="price-info-section"):
                gr.HTML("""
                    <h4 style="color: #667eea; margin-bottom: 15px;">
                        <span class="icon">📊</span>信息价载入功能
                    </h4>
                    <p style="color: #666; margin-bottom: 20px;">
                        智能从数据库中找到信息价数据，自动匹配资源编号并更新价格
                    </p>
                """)

                # 信息价数据库选择
                with gr.Row():
                    with gr.Column(scale=2):
                        price_info_databases = gr.Dropdown(
                            label="🗄️ 选择信息价数据库",
                            choices=[],
                            value=None,
                            info="系统将自动扫描可用的信息价数据库文件",
                            interactive=True
                        )

                    with gr.Column(scale=1):
                        scan_price_db_btn = gr.Button("🔍 扫描信息价数据库", elem_classes="btn-secondary")

                # 载入操作
                with gr.Row():
                    load_price_info_btn = gr.Button("📥 载入信息价", elem_classes="btn-success", size="lg")

                # 载入状态和结果
                price_info_load_status = gr.HTML(
                    value="<p style='color: #666;'>请先扫描并选择信息价数据库</p>",
                    label="载入状态"
                )

                # 匹配结果统计
                price_match_stats = gr.HTML(
                    value="",
                    label="匹配统计"
                )
            
            # 操作结果
            price_update_status = gr.HTML(
                value="<p style='color: #666;'>等待操作...</p>",
                label="操作状态"
            )

            # 隐藏状态变量
            selected_resource_code = gr.Textbox(
                value="",
                visible=False,
                interactive=False
            )

        return {
            'unique_resources_display': unique_resources_display,
            'selected_resource_info': selected_resource_info,
            'new_price_input': new_price_input,
            'update_price_btn': update_price_btn,
            'recalculate_btn': recalculate_btn,
            'load_resources_btn': load_resources_btn,
            'export_resources_btn': export_resources_btn,
            'price_update_status': price_update_status,
            'selected_resource_code': selected_resource_code,
            # 信息价载入相关组件
            'price_info_databases': price_info_databases,
            'scan_price_db_btn': scan_price_db_btn,
            'load_price_info_btn': load_price_info_btn,
            'price_info_load_status': price_info_load_status,
            'price_match_stats': price_match_stats
        }

    def create_complete_interface(self):
        """创建完整的高级定额管理界面"""
        with gr.Tab("🔗 数据库连接"):
            connection_components = self.create_database_connection_interface()
        
        with gr.Tab("📝 识别定额修订"):
            revision_components = self.create_quota_revision_interface()
        
        with gr.Tab("🔍 定额搜索"):
            search_components = self.create_quota_search_interface()
        
        with gr.Tab("💰 资源价格"):
            price_components = self.create_resource_price_interface()

        with gr.Tab("📊 信息价识别"):
            price_info_components = self.create_price_info_interface()

        # 绑定定额修订事件
        self._bind_revision_events(revision_components)

        return {
            'connection': connection_components,
            'revision': revision_components,
            'search': search_components,
            'price': price_components,
            'price_info': price_info_components
        }

    def create_price_info_interface(self, api_key_input=None):
        """创建信息价识别界面"""
        if not self.price_info_interface:
            with gr.Group():
                gr.HTML("""
                    <div style="text-align: center; padding: 20px; color: #666;">
                        <h3>⚠️ 信息价识别功能不可用</h3>
                        <p>PriceInfoInterface模块未正确加载</p>
                    </div>
                """)
            return {}

        # 信息价识别区域
        recognition_components = self.price_info_interface.create_price_info_recognition_interface()

        # 信息价合并区域
        merge_components = self.price_info_interface.create_price_merge_interface()

        # 绑定PDF预览相关事件
        self._bind_price_info_events(recognition_components, api_key_input)

        return {
            'recognition': recognition_components,
            'merge': merge_components
        }

    def _bind_price_info_events(self, components, api_key_input=None):
        """绑定信息价识别相关事件"""
        try:
            # PDF文件上传事件
            components['pdf_input'].upload(
                fn=self.price_info_interface.handle_pdf_upload,
                inputs=[components['pdf_input']],
                outputs=[
                    components['pdf_viewer'],
                    components['pdf_viewer'],  # 控制可见性
                    components['current_page'],
                    components['total_pages']
                ]
            )

            # 页面导航事件
            components['current_page'].change(
                fn=self.price_info_interface.show_page,
                inputs=[components['pdf_input'], components['current_page']],
                outputs=[components['pdf_viewer']]
            )

            components['prev_page_btn'].click(
                fn=self.price_info_interface.prev_page,
                inputs=[components['pdf_input'], components['current_page']],
                outputs=[components['current_page'], components['pdf_viewer']]
            )

            components['next_page_btn'].click(
                fn=self.price_info_interface.next_page,
                inputs=[
                    components['pdf_input'],
                    components['current_page'],
                    components['total_pages']
                ],
                outputs=[components['current_page'], components['pdf_viewer']]
            )

            components['zoom_btn'].click(
                fn=self.price_info_interface.zoom_page,
                inputs=[components['pdf_input'], components['current_page']],
                outputs=[components['pdf_viewer']]
            )

            # 处理按钮事件 - 传递API密钥
            if api_key_input:
                components['process_btn'].click(
                    fn=self.price_info_interface.process_price_info,
                    inputs=[
                        components['pdf_input'],
                        components['start_page'],
                        components['end_page'],
                        components['model_type'],
                        api_key_input  # 传递API密钥
                    ],
                    outputs=[
                        components['status_output'],
                        components['stats_output'],
                        components['preview_output'],
                        components['download_output']
                    ]
                )
            else:
                components['process_btn'].click(
                    fn=self.price_info_interface.process_price_info,
                    inputs=[
                        components['pdf_input'],
                        components['start_page'],
                        components['end_page'],
                        components['model_type']
                    ],
                    outputs=[
                        components['status_output'],
                        components['stats_output'],
                        components['preview_output'],
                        components['download_output']
                    ]
                )

        except Exception as e:
            print(f"绑定信息价识别事件失败: {str(e)}")

    def _bind_revision_events(self, components):
        """绑定定额修订相关事件"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                print("定额修订处理器未初始化")
                return

            revision_processor = self.quota_manager.revision_processor

            # 加载数据库事件
            components['load_revision_db_btn'].click(
                fn=self._handle_load_revision_database,
                inputs=[components['revision_db_path']],
                outputs=[
                    components['revision_status'],
                    components['quota_selector'],
                    components['revision_log']
                ]
            )

            # 刷新数据事件
            components['refresh_revision_data_btn'].click(
                fn=self._handle_refresh_revision_data,
                inputs=[],
                outputs=[
                    components['quota_selector'],
                    components['revision_log']
                ]
            )

            # 定额项选择事件
            components['quota_selector'].change(
                fn=self._handle_quota_selection,
                inputs=[components['quota_selector']],
                outputs=[
                    components['quota_code_edit'],
                    components['quota_name_edit'],
                    components['quota_unit_edit'],
                    components['quota_work_content_edit'],
                    components['resources_dataframe'],
                    components['revision_log']
                ]
            )

            # 保存定额信息事件
            components['save_quota_btn'].click(
                fn=self._handle_save_quota_info,
                inputs=[
                    components['quota_code_edit'],
                    components['quota_name_edit'],
                    components['quota_unit_edit'],
                    components['quota_work_content_edit']
                ],
                outputs=[
                    components['revision_status'],
                    components['revision_log']
                ]
            )

            # 保存资源数据事件
            components['save_resources_btn'].click(
                fn=self._handle_save_resources,
                inputs=[
                    components['quota_code_edit'],
                    components['resources_dataframe']
                ],
                outputs=[
                    components['revision_status'],
                    components['revision_log']
                ]
            )

            # 删除定额事件
            components['delete_quota_btn'].click(
                fn=self._handle_delete_quota,
                inputs=[components['quota_code_edit']],
                outputs=[
                    components['revision_status'],
                    components['quota_selector'],
                    components['revision_log']
                ]
            )

            # PDF相关事件
            components['refresh_pdf_list_btn'].click(
                fn=self._handle_refresh_pdf_list,
                inputs=[],
                outputs=[components['pdf_file_selector']]
            )

            components['pdf_file_selector'].change(
                fn=self._handle_pdf_selection,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[components['pdf_preview']]
            )

            components['pdf_page_input'].change(
                fn=self._handle_pdf_page_change,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[components['pdf_preview']]
            )

            components['prev_pdf_page_btn'].click(
                fn=self._handle_prev_pdf_page,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[
                    components['pdf_page_input'],
                    components['pdf_preview']
                ]
            )

            components['next_pdf_page_btn'].click(
                fn=self._handle_next_pdf_page,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[
                    components['pdf_page_input'],
                    components['pdf_preview']
                ]
            )

        except Exception as e:
            print(f"绑定定额修订事件失败: {str(e)}")

    def _handle_load_revision_database(self, db_path):
        """处理加载修订数据库"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 定额修订处理器未初始化", [], "❌ 处理器未初始化"

            success, message, quota_list = self.quota_manager.revision_processor.load_database(db_path)

            if success:
                quota_choices = quota_list
                log_message = f"✅ 成功加载数据库: {db_path}"
            else:
                quota_choices = []
                log_message = f"❌ 加载失败: {message}"

            return message, quota_choices, log_message

        except Exception as e:
            error_msg = f"❌ 加载数据库异常: {str(e)}"
            return error_msg, [], error_msg

    def _handle_refresh_revision_data(self):
        """处理刷新修订数据"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return [], "❌ 处理器未初始化"

            quota_list = self.quota_manager.revision_processor._get_quota_list()
            return quota_list, "🔄 数据已刷新"

        except Exception as e:
            return [], f"❌ 刷新失败: {str(e)}"

    def _handle_quota_selection(self, quota_selection):
        """处理定额项选择"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "", "", "", "", [], "❌ 处理器未初始化"

            if not quota_selection:
                return "", "", "", "", [], "请选择定额项"

            quota_info, resources_data = self.quota_manager.revision_processor.get_quota_details(quota_selection)

            log_message = f"📋 已选择定额: {quota_selection}"

            return (
                quota_info.get('code', ''),
                quota_info.get('name', ''),
                quota_info.get('unit', ''),
                quota_info.get('work_content', ''),
                resources_data,
                log_message
            )

        except Exception as e:
            error_msg = f"❌ 获取定额详情失败: {str(e)}"
            return "", "", "", "", [], error_msg

    def _handle_save_quota_info(self, quota_code, quota_name, unit, work_content):
        """处理保存定额信息"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 处理器未初始化", "❌ 处理器未初始化"

            if not quota_code:
                return "❌ 定额编码不能为空", "❌ 定额编码为空"

            success, message = self.quota_manager.revision_processor.save_quota_info(
                quota_code, quota_name, unit, work_content
            )

            log_message = f"💾 保存定额信息: {quota_code} - {message}"
            return message, log_message

        except Exception as e:
            error_msg = f"❌ 保存定额信息异常: {str(e)}"
            return error_msg, error_msg

    def _handle_save_resources(self, quota_code, resources_data):
        """处理保存资源数据"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 处理器未初始化", "❌ 处理器未初始化"

            if not quota_code:
                return "❌ 定额编码不能为空", "❌ 定额编码为空"

            success, message = self.quota_manager.revision_processor.save_resources_data(
                quota_code, resources_data
            )

            log_message = f"💾 保存资源数据: {quota_code} - {message}"
            return message, log_message

        except Exception as e:
            error_msg = f"❌ 保存资源数据异常: {str(e)}"
            return error_msg, error_msg

    def _handle_delete_quota(self, quota_code):
        """处理删除定额"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 处理器未初始化", [], "❌ 处理器未初始化"

            if not quota_code:
                return "❌ 定额编码不能为空", [], "❌ 定额编码为空"

            success, message = self.quota_manager.revision_processor.delete_quota(quota_code)

            if success:
                # 刷新定额列表
                quota_list = self.quota_manager.revision_processor._get_quota_list()
                log_message = f"🗑️ 删除定额: {quota_code} - {message}"
                return message, quota_list, log_message
            else:
                log_message = f"❌ 删除失败: {quota_code} - {message}"
                return message, [], log_message

        except Exception as e:
            error_msg = f"❌ 删除定额异常: {str(e)}"
            return error_msg, [], error_msg

    def _handle_refresh_pdf_list(self):
        """处理刷新PDF列表"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return []

            pdf_files = self.quota_manager.revision_processor.get_available_pdfs()
            return pdf_files

        except Exception as e:
            print(f"刷新PDF列表失败: {str(e)}")
            return []

    def _handle_pdf_selection(self, pdf_path, page_num):
        """处理PDF文件选择"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return None

            if not pdf_path:
                return None

            success, message, image_path = self.quota_manager.revision_processor.render_pdf_page(
                pdf_path, int(page_num) if page_num else 1
            )

            return image_path if success else None

        except Exception as e:
            print(f"PDF选择失败: {str(e)}")
            return None

    def _handle_pdf_page_change(self, pdf_path, page_num):
        """处理PDF页面变化"""
        return self._handle_pdf_selection(pdf_path, page_num)

    def _handle_prev_pdf_page(self, pdf_path, current_page):
        """处理上一页"""
        try:
            if not pdf_path:
                return current_page, None

            new_page = max(1, int(current_page) - 1)
            image_path = self._handle_pdf_selection(pdf_path, new_page)

            return new_page, image_path

        except Exception as e:
            print(f"上一页失败: {str(e)}")
            return current_page, None

    def _handle_next_pdf_page(self, pdf_path, current_page):
        """处理下一页"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return current_page, None

            if not pdf_path:
                return current_page, None

            # 获取PDF总页数
            total_pages = self.quota_manager.revision_processor.get_pdf_page_count(pdf_path)
            new_page = min(total_pages, int(current_page) + 1)
            image_path = self._handle_pdf_selection(pdf_path, new_page)

            return new_page, image_path

        except Exception as e:
            print(f"下一页失败: {str(e)}")
            return current_page, None
