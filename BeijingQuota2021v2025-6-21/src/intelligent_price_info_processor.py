#!/usr/bin/env python3
"""
智能信息价识别处理器
Intelligent Price Information Recognition Processor
专门处理北京市造价信息PDF的识别、提取和数据库存储
基于定额识别模块的架构设计，独立处理信息价识别任务
"""

import os
import pandas as pd
import json
import re
import asyncio
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime
from pathlib import Path

from .config import Config
from .pdf_processor import PDFProcessor
from .lm_studio_manager import LMStudioManager

class IntelligentPriceInfoProcessor:
    """智能信息价识别处理器"""
    
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        self.pdf_processor = PDFProcessor()

        # 初始化LM Studio管理器
        self.lm_studio_manager = LMStudioManager()

        # 基础支持的AI模型
        self.base_models = {
            "qwen_qvq_max": "阿里通义千问-QVQ-Max"
        }

        # 动态支持的模型（包含LM Studio模型）
        self.supported_models = self.base_models.copy()
        
        # API配置
        self.api_keys = {
            "dashscope": "",
            "openai": "",
            "anthropic": ""
        }
        
        # 加载API密钥
        self.load_api_keys()
        
        # 请求会话
        import requests
        self.session = requests.Session()
        self.session.verify = True

        # 保存最后生成的文件路径，用于数据库写入
        self.last_csv_file = None
        self.last_json_file = None
        
    def load_api_keys(self):
        """加载API密钥"""
        try:
            # 从环境变量加载
            self.api_keys["dashscope"] = os.getenv("DASHSCOPE_API_KEY", "")
            self.api_keys["openai"] = os.getenv("OPENAI_API_KEY", "")
            self.api_keys["anthropic"] = os.getenv("ANTHROPIC_API_KEY", "")
            
            self.logger.info("API密钥加载完成")
        except Exception as e:
            self.logger.error(f"加载API密钥失败: {str(e)}")
    
    def set_api_key(self, provider: str, api_key: str):
        """设置API密钥"""
        if provider in self.api_keys:
            self.api_keys[provider] = api_key
            # 同时设置到环境变量
            if provider == "dashscope":
                os.environ["DASHSCOPE_API_KEY"] = api_key
            elif provider == "openai":
                os.environ["OPENAI_API_KEY"] = api_key
            elif provider == "anthropic":
                os.environ["ANTHROPIC_API_KEY"] = api_key
            
            self.logger.info(f"已设置 {provider} API密钥")
            return True
        return False
    
    def get_api_key_status(self) -> Dict[str, bool]:
        """获取API密钥状态"""
        return {
            provider: bool(key.strip()) 
            for provider, key in self.api_keys.items()
        }
    
    async def process_price_info_pdf(
        self,
        pdf_path: str,
        start_page: int = 1,
        end_page: int = None,
        model_type: str = "qwen_qvq_max",
        output_dir: str = "output/price_info"
    ) -> Tuple[bool, str, Dict]:
        """
        处理信息价PDF文件
        
        Args:
            pdf_path: PDF文件路径
            start_page: 开始页码
            end_page: 结束页码
            model_type: AI模型类型
            output_dir: 输出目录
            
        Returns:
            (成功标志, 状态消息, 处理结果)
        """
        try:
            self.logger.info(f"开始处理信息价PDF: {pdf_path}")
            
            # 1. 转换PDF为图片
            self.logger.info("转换PDF页面为图片...")
            image_paths = await self.pdf_processor.extract_pages_as_images(
                pdf_path, start_page, end_page or start_page
            )
            
            if not image_paths:
                return False, "PDF转换失败，未生成图片", {}
            
            # 2. 逐页识别信息价数据
            all_price_data = []
            processing_stats = {
                'total_pages': len(image_paths),
                'processed_pages': 0,
                'total_chapters': 0,
                'total_price_items': 0,
                'failed_pages': []
            }
            
            for i, image_path in enumerate(image_paths):
                page_num = start_page + i
                self.logger.info(f"处理第 {page_num} 页...")
                
                try:
                    # 使用AI模型识别信息价数据
                    recognition_result = await self._process_image_with_ai(
                        image_path, model_type
                    )
                    
                    if recognition_result:
                        # 解析识别结果
                        page_data = self._parse_price_info_result(recognition_result, page_num)
                        if page_data:
                            all_price_data.extend(page_data)
                            processing_stats['processed_pages'] += 1
                            processing_stats['total_chapters'] += len(page_data)
                            processing_stats['total_price_items'] += sum(
                                len(chapter.get('price_items', [])) for chapter in page_data
                            )
                        else:
                            processing_stats['failed_pages'].append(page_num)
                    else:
                        processing_stats['failed_pages'].append(page_num)
                        
                except Exception as e:
                    self.logger.error(f"处理第 {page_num} 页时出错: {str(e)}")
                    processing_stats['failed_pages'].append(page_num)
            
            # 3. 生成输出文件
            if all_price_data:
                output_files = await self._generate_output_files(all_price_data, output_dir)
                processing_stats['output_files'] = output_files
                
                success_msg = f"✅ 信息价识别完成！\n"
                success_msg += f"📊 处理统计:\n"
                success_msg += f"   - 总页数: {processing_stats['total_pages']}\n"
                success_msg += f"   - 成功页数: {processing_stats['processed_pages']}\n"
                success_msg += f"   - 识别章节: {processing_stats['total_chapters']}\n"
                success_msg += f"   - 价格条目: {processing_stats['total_price_items']}\n"
                
                if processing_stats['failed_pages']:
                    success_msg += f"   - 失败页面: {processing_stats['failed_pages']}\n"
                
                success_msg += f"📁 输出文件: {len(output_files)} 个"
                
                return True, success_msg, processing_stats
            else:
                return False, "未识别到有效的信息价数据", processing_stats
                
        except Exception as e:
            error_msg = f"信息价处理失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}
    
    async def _process_image_with_ai(self, image_path: str, model_type: str) -> Optional[str]:
        """
        使用AI模型处理图片
        
        Args:
            image_path: 图片路径
            model_type: 模型类型
            
        Returns:
            Optional[str]: 识别结果
        """
        try:
            if model_type == "qwen_qvq_max":
                return await self._process_with_qwen_qvq(image_path)
            elif model_type.startswith("lm_studio_"):
                # 获取实际的模型ID
                actual_model_id = self.get_model_id_from_key(model_type)
                return await self._process_with_lm_studio(image_path, actual_model_id)
            elif model_type == "openai_gpt4v":
                return await self._process_with_openai(image_path)
            else:
                self.logger.error(f"不支持的模型类型: {model_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"AI模型处理失败: {str(e)}")
            return None
    
    async def _process_with_qwen_qvq(self, image_path: str) -> Optional[str]:
        """使用阿里云通义千问QVQ模型处理"""
        try:
            if not self.api_keys["dashscope"]:
                self.logger.error("未设置DASHSCOPE_API_KEY")
                return None

            # 首先尝试使用阿里云SDK
            try:
                result = await self._process_with_dashscope_sdk(image_path)
                if result:
                    return result
            except Exception as e:
                self.logger.warning(f"SDK调用失败，尝试HTTP调用: {str(e)}")

            # 如果SDK失败，尝试HTTP调用
            return await self._process_with_dashscope_http(image_path)

        except Exception as e:
            self.logger.error(f"QVQ模型处理失败: {str(e)}")
            return None

    async def _process_with_dashscope_sdk(self, image_path: str) -> Optional[str]:
        """使用阿里云DashScope SDK处理"""
        try:
            # 尝试导入DashScope SDK
            try:
                import dashscope
                from dashscope import MultiModalConversation
            except ImportError:
                self.logger.warning("未安装dashscope SDK，请运行: pip install dashscope")
                return None

            # 设置API密钥
            dashscope.api_key = self.api_keys["dashscope"]

            # 模型回退策略
            fallback_models = ["qvq-max", "qwen-vl-max", "qwen-vl-plus"]

            for try_model in fallback_models:
                self.logger.info(f"尝试使用SDK模型: {try_model}")

                try:
                    # 准备消息
                    messages = [
                        {
                            "role": "user",
                            "content": [
                                {"image": f"file://{image_path}"},
                                {"text": self.config.PRICE_INFO_EXTRACTION_PROMPT}
                            ]
                        }
                    ]

                    # 调用模型
                    response = MultiModalConversation.call(
                        model=try_model,
                        messages=messages,
                        max_tokens=4000,
                        temperature=0.1
                    )

                    if response.status_code == 200:
                        self.logger.info(f"✅ SDK模型 {try_model} 调用成功")
                        return response.output.choices[0].message.content
                    else:
                        self.logger.warning(f"❌ SDK模型 {try_model} 调用失败: {response.message}")
                        continue

                except Exception as e:
                    self.logger.warning(f"❌ SDK模型 {try_model} 处理失败: {str(e)}")
                    continue

            return None

        except Exception as e:
            self.logger.warning(f"SDK处理失败: {str(e)}")
            return None

    async def _process_with_dashscope_http(self, image_path: str) -> Optional[str]:
        """使用HTTP方式调用DashScope"""
        try:
            base64_image = self._encode_image_to_base64(image_path)

            headers = {
                "Authorization": f"Bearer {self.api_keys['dashscope']}",
                "Content-Type": "application/json"
            }

            # 只尝试支持的模型
            fallback_models = ["qwen-vl-max", "qwen-vl-plus"]

            for try_model in fallback_models:
                self.logger.info(f"尝试使用HTTP模型: {try_model}")

                data = {
                    "model": try_model,
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{base64_image}"
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": self.config.PRICE_INFO_EXTRACTION_PROMPT
                                }
                            ]
                        }
                    ],
                    "max_tokens": 4000,
                    "temperature": 0.1
                }

                try:
                    # 使用不同的端点
                    endpoints = [
                        "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
                        "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
                    ]

                    for endpoint in endpoints:
                        try:
                            response = self.session.post(
                                endpoint,
                                headers=headers,
                                json=data,
                                timeout=120,
                                proxies={}  # 禁用代理
                            )

                            if response.status_code == 200:
                                result = response.json()
                                self.logger.info(f"✅ HTTP模型 {try_model} 调用成功")

                                # 处理不同的响应格式
                                if 'choices' in result:
                                    return result['choices'][0]['message']['content']
                                elif 'output' in result:
                                    return result['output']['text']
                                else:
                                    self.logger.warning(f"未知的响应格式: {result}")
                                    continue
                            else:
                                self.logger.warning(f"❌ HTTP模型 {try_model} 请求失败: {response.status_code}")
                                continue

                        except Exception as e:
                            self.logger.warning(f"❌ 端点 {endpoint} 调用失败: {str(e)}")
                            continue

                except Exception as e:
                    self.logger.warning(f"❌ HTTP模型 {try_model} 处理失败: {str(e)}")
                    continue

            return None

        except Exception as e:
            self.logger.error(f"HTTP调用失败: {str(e)}")
            return None
    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        import base64

        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    async def _process_with_lm_studio(self, image_path: str, model_id: str = None) -> Optional[str]:
        """使用LM Studio本地模型处理"""
        try:
            # 首先检查LM Studio是否运行
            try:
                test_response = self.session.get("http://127.0.0.1:1234/v1/models", timeout=5)
                if test_response.status_code != 200:
                    self.logger.error("LM Studio服务未运行或不可访问")
                    return None
            except Exception as e:
                self.logger.error(f"无法连接到LM Studio: {str(e)}")
                return None

            base64_image = self._encode_image_to_base64(image_path)

            headers = {
                "Content-Type": "application/json"
            }

            # 使用传入的模型ID，如果没有则使用默认值
            actual_model = model_id if model_id else "qwen2.5-vl-7b"

            data = {
                "model": actual_model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            },
                            {
                                "type": "text",
                                "text": self.config.PRICE_INFO_EXTRACTION_PROMPT
                            }
                        ]
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.1
            }

            self.logger.info("调用LM Studio模型...")
            response = self.session.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=300,  # 增加超时时间
                proxies={}  # 禁用代理
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.info("✅ LM Studio模型调用成功")
                return result['choices'][0]['message']['content']
            else:
                self.logger.error(f"❌ LM Studio请求失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    self.logger.error(f"   错误详情: {error_detail}")
                except:
                    self.logger.error(f"   响应内容: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"LM Studio模型处理失败: {str(e)}")
            return None

    async def _process_with_openai(self, image_path: str) -> Optional[str]:
        """使用OpenAI GPT-4V模型处理"""
        try:
            if not self.api_keys["openai"]:
                self.logger.error("未设置OPENAI_API_KEY")
                return None

            base64_image = self._encode_image_to_base64(image_path)

            headers = {
                "Authorization": f"Bearer {self.api_keys['openai']}",
                "Content-Type": "application/json"
            }

            data = {
                "model": "gpt-4-vision-preview",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            },
                            {
                                "type": "text",
                                "text": self.config.PRICE_INFO_EXTRACTION_PROMPT
                            }
                        ]
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.1
            }

            response = self.session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                self.logger.info("✅ OpenAI模型调用成功")
                return result['choices'][0]['message']['content']
            else:
                self.logger.error(f"❌ OpenAI请求失败: {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"OpenAI模型处理失败: {str(e)}")
            return None

    def _parse_price_info_result(self, recognition_result, page_num: int) -> List[Dict]:
        """
        解析AI识别结果

        Args:
            recognition_result: AI识别的原始结果（可能是字符串或列表）
            page_num: 页码

        Returns:
            解析后的价格信息列表
        """
        try:
            # 首先处理结果格式，统一转换为字符串
            text_result = self._normalize_result_format(recognition_result, page_num)
            if not text_result:
                return []

            # 尝试JSON解析
            json_data = self._try_parse_json(text_result, page_num)
            if json_data:
                return json_data

            # 如果JSON解析失败，尝试文本解析
            self.logger.info(f"第 {page_num} 页JSON解析失败，尝试文本解析...")
            text_data = self._parse_text_result(text_result, page_num)
            if text_data:
                return text_data

            # 如果都失败了，记录原始结果用于调试
            self.logger.warning(f"第 {page_num} 页解析失败，原始结果: {str(text_result)[:500]}...")
            return []

        except Exception as e:
            self.logger.error(f"第 {page_num} 页数据解析失败: {str(e)}")
            return []

    def _normalize_result_format(self, recognition_result, page_num: int) -> str:
        """
        标准化AI识别结果格式

        Args:
            recognition_result: AI识别的原始结果
            page_num: 页码

        Returns:
            标准化后的字符串结果
        """
        try:
            # 如果是字符串，直接返回
            if isinstance(recognition_result, str):
                return recognition_result

            # 如果是列表，尝试提取文本内容
            elif isinstance(recognition_result, list):
                self.logger.info(f"第 {page_num} 页检测到列表格式结果，尝试提取文本...")

                # 尝试不同的提取方式
                text_parts = []

                for item in recognition_result:
                    if isinstance(item, dict):
                        # 尝试常见的文本字段
                        for text_field in ['text', 'content', 'message', 'output']:
                            if text_field in item:
                                text_parts.append(str(item[text_field]))
                                break
                        else:
                            # 如果没有找到文本字段，尝试转换整个字典
                            text_parts.append(str(item))
                    elif isinstance(item, str):
                        text_parts.append(item)
                    else:
                        text_parts.append(str(item))

                if text_parts:
                    combined_text = '\n'.join(text_parts)
                    self.logger.info(f"第 {page_num} 页成功提取文本，长度: {len(combined_text)}")
                    return combined_text
                else:
                    self.logger.warning(f"第 {page_num} 页列表格式结果为空")
                    return ""

            # 如果是字典，尝试提取文本内容
            elif isinstance(recognition_result, dict):
                self.logger.info(f"第 {page_num} 页检测到字典格式结果，尝试提取文本...")

                # 尝试常见的文本字段
                for text_field in ['text', 'content', 'message', 'output', 'choices']:
                    if text_field in recognition_result:
                        field_value = recognition_result[text_field]

                        # 如果是choices字段，可能需要进一步提取
                        if text_field == 'choices' and isinstance(field_value, list) and field_value:
                            if isinstance(field_value[0], dict) and 'message' in field_value[0]:
                                return str(field_value[0]['message'].get('content', ''))

                        return str(field_value)

                # 如果没有找到文本字段，转换整个字典
                return str(recognition_result)

            # 其他类型，直接转换为字符串
            else:
                self.logger.warning(f"第 {page_num} 页未知结果格式: {type(recognition_result)}")
                return str(recognition_result)

        except Exception as e:
            self.logger.error(f"第 {page_num} 页结果格式标准化失败: {str(e)}")
            return str(recognition_result) if recognition_result else ""

    def _extract_json_from_markdown(self, text: str) -> str:
        """从markdown代码块中提取JSON"""
        try:
            # 尝试提取```json```代码块
            json_patterns = [
                r'```json\s*\n(.*?)\n```',  # ```json ... ```
                r'```\s*\n(.*?)\n```',     # ``` ... ```
                r'`(.*?)`',                # `...`
            ]

            for pattern in json_patterns:
                matches = re.findall(pattern, text, re.DOTALL)
                for match in matches:
                    # 检查是否包含JSON结构
                    if '{' in match and '}' in match:
                        return match.strip()

            return ""

        except Exception as e:
            self.logger.debug(f"提取markdown JSON失败: {str(e)}")
            return ""

    def _try_parse_json(self, recognition_result: str, page_num: int) -> List[Dict]:
        """尝试JSON解析"""
        try:
            # 清理结果文本
            cleaned_result = recognition_result.strip()

            # 首先尝试提取markdown代码块中的JSON
            json_text = self._extract_json_from_markdown(cleaned_result)
            if not json_text:
                json_text = cleaned_result

            # 尝试直接解析
            if json_text.strip().startswith('{'):
                try:
                    data = json.loads(json_text.strip())
                except json.JSONDecodeError as e:
                    self.logger.debug(f"第 {page_num} 页直接JSON解析失败: {str(e)}")
                    data = None
            else:
                data = None

            # 如果直接解析失败，尝试提取JSON部分
            if not data:
                json_patterns = [
                    r'\{[^{}]*"chapters"[^{}]*\[[^\]]*\][^{}]*\}',  # 简单JSON
                    r'\{.*?"chapters".*?\[.*?\].*?\}',  # 包含chapters的JSON
                    r'\{.*\}',  # 任何JSON对象
                ]

                for pattern in json_patterns:
                    json_match = re.search(pattern, json_text, re.DOTALL)
                    if json_match:
                        try:
                            data = json.loads(json_match.group())
                            self.logger.debug(f"第 {page_num} 页使用正则表达式成功提取JSON")
                            break
                        except json.JSONDecodeError:
                            continue

                if not data:
                    return None

            # 验证数据结构
            if not isinstance(data, dict):
                return None

            # 如果没有chapters字段，尝试构建
            if 'chapters' not in data:
                if 'price_items' in data:
                    data = {
                        'page_header': data.get('page_header', '工程造价信息'),
                        'chapters': [{
                            'chapter_code': '01',
                            'chapter_name': '信息价数据',
                            'remarks': '',
                            'price_items': data['price_items']
                        }]
                    }
                else:
                    return None

            # 处理数据
            page_header = data.get('page_header', '工程造价信息')
            page_footer = data.get('page_footer', '')
            chapters = data.get('chapters', [])

            # 为每个章节添加页面、页眉和页脚信息
            for chapter in chapters:
                chapter['page_number'] = page_num
                chapter['page_header'] = page_header
                chapter['page_footer'] = page_footer

                # 验证价格条目数据
                price_items = chapter.get('price_items', [])
                validated_items = []

                for item in price_items:
                    if self._validate_price_item(item):
                        validated_items.append(item)

                chapter['price_items'] = validated_items

            return chapters

        except json.JSONDecodeError as e:
            self.logger.debug(f"第 {page_num} 页JSON解析失败: {str(e)}")
            return None
        except Exception as e:
            self.logger.debug(f"第 {page_num} 页JSON处理失败: {str(e)}")
            return None

    def _parse_text_result(self, recognition_result: str, page_num: int) -> List[Dict]:
        """解析文本格式的识别结果"""
        try:
            # 初始化结果
            chapters = []
            current_chapter = {
                'page_number': page_num,
                'page_header': '工程造价信息',
                'page_footer': '',
                'chapter_code': '01',
                'chapter_name': '信息价数据',
                'remarks': '',
                'price_items': []
            }

            # 按行分析文本
            lines = recognition_result.split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 尝试识别页眉信息
                if any(keyword in line for keyword in ['工程造价信息', '市场参考价', '厂家参考价']):
                    current_chapter['page_header'] = line
                    continue

                # 尝试识别页脚时间信息
                footer_match = re.search(r'(\d{4}年\d{1,2}月)', line)
                if footer_match:
                    current_chapter['page_footer'] = footer_match.group(1)
                    continue

                # 尝试识别章节信息
                chapter_match = re.search(r'(\d+)\.?\s*([^（]+)（编码[：:]\s*(\d+)\）', line)
                if chapter_match:
                    current_chapter['chapter_code'] = chapter_match.group(3)
                    current_chapter['chapter_name'] = chapter_match.group(2).strip()
                    continue

                # 尝试识别价格条目
                price_item = self._extract_price_item_from_line(line)
                if price_item:
                    current_chapter['price_items'].append(price_item)
                    continue

            # 如果找到了价格条目，添加到结果中
            if current_chapter['price_items']:
                chapters.append(current_chapter)

            return chapters

        except Exception as e:
            self.logger.error(f"第 {page_num} 页文本解析失败: {str(e)}")
            return []

    def _extract_price_item_from_line(self, line: str) -> Dict:
        """从文本行中提取价格条目信息"""
        try:
            # 清理行内容
            line = line.strip()
            if not line or len(line) < 10:
                return None

            # 尝试匹配常见的价格信息格式
            patterns = [
                # 格式1: 10位资源编号 产品名称 规格型号 单位 含税价 不含税价
                r'(\d{10})\s+([^\d]+?)\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)\s+(\d+\.?\d*)',
                # 格式2: 10位资源编号 产品名称 规格型号 单位 价格
                r'(\d{10})\s+([^\d]+?)\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)',
                # 格式3: 资源编号 产品名称 单位 含税价 不含税价
                r'(\d{8,})\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)\s+(\d+\.?\d*)',
                # 格式4: 简单格式 - 编号 名称 单位 价格
                r'(\d{6,})\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)',
            ]

            for i, pattern in enumerate(patterns, 1):
                match = re.search(pattern, line)
                if match:
                    groups = match.groups()
                    self.logger.debug(f"使用模式 {i} 匹配成功: {groups}")

                    if len(groups) >= 4:
                        # 根据匹配的组数确定字段映射
                        if len(groups) == 6:  # 完整格式
                            return {
                                'resource_code': groups[0],
                                'product_name': groups[1].strip(),
                                'specifications': groups[2].strip(),
                                'unit': groups[3].strip(),
                                'price_with_tax': groups[4],
                                'price_without_tax': groups[5]
                            }
                        elif len(groups) == 5:  # 5个字段
                            if i <= 2:  # 前两个模式
                                return {
                                    'resource_code': groups[0],
                                    'product_name': groups[1].strip(),
                                    'specifications': groups[2].strip(),
                                    'unit': groups[3].strip(),
                                    'price_with_tax': groups[4],
                                    'price_without_tax': ''
                                }
                            else:  # 后面的模式
                                return {
                                    'resource_code': groups[0],
                                    'product_name': groups[1].strip(),
                                    'specifications': '',
                                    'unit': groups[2].strip(),
                                    'price_with_tax': groups[3],
                                    'price_without_tax': groups[4]
                                }
                        elif len(groups) == 4:  # 4个字段
                            return {
                                'resource_code': groups[0],
                                'product_name': groups[1].strip(),
                                'specifications': '',
                                'unit': groups[2].strip(),
                                'price_with_tax': groups[3],
                                'price_without_tax': ''
                            }

            return None

        except Exception as e:
            self.logger.debug(f"提取价格条目失败: {str(e)}")
            return None

    def _validate_price_item(self, item: Dict) -> bool:
        """验证价格条目数据的有效性"""
        required_fields = ['resource_code', 'product_name', 'unit']

        # 检查必需字段
        for field in required_fields:
            if not item.get(field):
                self.logger.debug(f"价格条目缺少必需字段: {field}")
                return False

        # 检查资源编号格式（允许数字、连字符、字母）
        resource_code = str(item.get('resource_code', '')).strip()
        if not resource_code:
            self.logger.debug("资源编号为空")
            return False

        # 资源编号应该包含数字，可以包含连字符和字母
        if not re.search(r'\d', resource_code):
            self.logger.debug(f"资源编号格式无效: {resource_code}")
            return False

        # 产品名称不能为空
        product_name = str(item.get('product_name', '')).strip()
        if not product_name:
            self.logger.debug("产品名称为空")
            return False

        # 单位不能为空
        unit = str(item.get('unit', '')).strip()
        if not unit:
            self.logger.debug("单位为空")
            return False

        self.logger.debug(f"价格条目验证通过: {resource_code} - {product_name}")
        return True

    async def _generate_output_files(self, price_data: List[Dict], output_dir: str) -> List[str]:
        """
        生成输出文件

        Args:
            price_data: 价格数据列表
            output_dir: 输出目录

        Returns:
            生成的文件路径列表
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_files = []

            # 1. 生成CSV文件
            csv_data = []

            for chapter in price_data:
                page_header = chapter.get('page_header', '')
                page_footer = chapter.get('page_footer', '')
                chapter_code = chapter.get('chapter_code', '')
                chapter_name = chapter.get('chapter_name', '')
                remarks = chapter.get('remarks', '')

                # 整合页眉和页脚信息
                header_footer_info = page_header
                if page_footer:
                    header_footer_info += f" ({page_footer})"

                for item in chapter.get('price_items', []):
                    csv_row = {
                        '信息价标识': header_footer_info,  # 第1列：整合页眉和页脚
                        '章节编号': chapter_code,
                        '章节名称': chapter_name,
                        '资源编号': item.get('resource_code', ''),
                        '产品名称': item.get('product_name', ''),
                        '规格型号及特征': item.get('specifications', ''),
                        '计量单位': item.get('unit', ''),
                        '市场参考价（含税）': item.get('price_with_tax', ''),
                        '市场参考价（不含税）': item.get('price_without_tax', ''),
                        '备注': remarks
                    }
                    csv_data.append(csv_row)

            if csv_data:
                csv_file = os.path.join(output_dir, f"price_info_result_{timestamp}.csv")
                df = pd.DataFrame(csv_data)
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                output_files.append(csv_file)
                self.logger.info(f"生成CSV文件: {csv_file}")

            # 2. 生成JSON文件
            json_file = os.path.join(output_dir, f"price_info_result_{timestamp}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'metadata': {
                        'export_date': datetime.now().isoformat(),
                        'total_chapters': len(price_data),
                        'total_items': sum(len(ch.get('price_items', [])) for ch in price_data),
                        'format': 'Price Information Export'
                    },
                    'data': price_data
                }, f, ensure_ascii=False, indent=2)
            output_files.append(json_file)
            self.logger.info(f"生成JSON文件: {json_file}")

            # 保存最后生成的文件路径，用于数据库写入
            if output_files:
                for file_path in output_files:
                    if file_path.endswith('.csv'):
                        self.last_csv_file = file_path
                    elif file_path.endswith('.json'):
                        self.last_json_file = file_path

            return output_files

        except Exception as e:
            self.logger.error(f"生成输出文件失败: {str(e)}")
            return []

    def merge_price_info_with_quotas(
        self,
        price_info_file: str,
        quota_resources_file: str,
        output_dir: str = "output/price_info"
    ) -> Tuple[bool, str, str]:
        """
        将信息价数据与定额资源数据进行智能合并

        Args:
            price_info_file: 信息价CSV文件路径
            quota_resources_file: 定额资源CSV文件路径
            output_dir: 输出目录

        Returns:
            (成功标志, 状态消息, 输出文件路径)
        """
        try:
            # 读取数据文件
            price_df = pd.read_csv(price_info_file, encoding='utf-8-sig')
            quota_df = pd.read_csv(quota_resources_file, encoding='utf-8-sig')

            # 基于资源编号进行匹配
            merged_data = []
            match_count = 0

            for _, quota_row in quota_df.iterrows():
                quota_resource_code = str(quota_row.get('资源编号', '')).strip()

                # 查找匹配的信息价数据
                price_matches = price_df[price_df['资源编号'].astype(str).str.strip() == quota_resource_code]

                if not price_matches.empty:
                    # 找到匹配的信息价
                    price_row = price_matches.iloc[0]
                    match_count += 1

                    # 合并数据
                    merged_row = quota_row.to_dict()
                    merged_row.update({
                        '信息价_产品名称': price_row.get('产品名称', ''),
                        '信息价_规格型号': price_row.get('规格型号及特征', ''),
                        '信息价_含税价格': price_row.get('市场参考价（含税）', ''),
                        '信息价_不含税价格': price_row.get('市场参考价（不含税）', ''),
                        '信息价_章节': price_row.get('章节名称', ''),
                        '匹配状态': '已匹配'
                    })
                else:
                    # 未找到匹配的信息价
                    merged_row = quota_row.to_dict()
                    merged_row.update({
                        '信息价_产品名称': '',
                        '信息价_规格型号': '',
                        '信息价_含税价格': '',
                        '信息价_不含税价格': '',
                        '信息价_章节': '',
                        '匹配状态': '未匹配'
                    })

                merged_data.append(merged_row)

            # 生成输出文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(output_dir, f"merged_quota_price_info_{timestamp}.csv")

            merged_df = pd.DataFrame(merged_data)
            merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            success_msg = f"✅ 信息价与定额数据合并完成！\n"
            success_msg += f"📊 合并统计:\n"
            success_msg += f"   - 定额资源总数: {len(quota_df)}\n"
            success_msg += f"   - 匹配成功数量: {match_count}\n"
            success_msg += f"   - 匹配率: {match_count/len(quota_df)*100:.1f}%\n"
            success_msg += f"📁 输出文件: {output_file}"

            return True, success_msg, output_file

        except Exception as e:
            error_msg = f"合并失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, ""

    async def test_model_connection(self, model_type: str) -> tuple[bool, str]:
        """
        测试AI模型连接

        Args:
            model_type: 模型类型

        Returns:
            (是否成功, 状态消息)
        """
        try:
            if model_type == "qwen_qvq_max":
                return await self._test_qwen_connection()
            elif model_type.startswith("lm_studio_"):
                # 获取实际的模型ID并测试
                actual_model_id = self.get_model_id_from_key(model_type)
                success, message = self.lm_studio_manager.test_model_connection(actual_model_id)
                return success, message
            elif model_type == "openai_gpt4v":
                return await self._test_openai_connection()
            else:
                return False, f"不支持的模型类型: {model_type}"

        except Exception as e:
            return False, f"连接测试失败: {str(e)}"

    async def _test_qwen_connection(self) -> tuple[bool, str]:
        """测试阿里云QVQ模型连接"""
        try:
            if not self.api_keys["dashscope"]:
                return False, "❌ 未设置DASHSCOPE_API_KEY"

            # 尝试使用DashScope SDK
            try:
                import dashscope
                from dashscope import Generation

                dashscope.api_key = self.api_keys["dashscope"]

                # 简单的文本生成测试
                response = Generation.call(
                    model='qwen-max',
                    prompt='测试连接',
                    max_tokens=5
                )

                if response.status_code == 200:
                    return True, "✅ 阿里云DashScope SDK连接正常"
                else:
                    return False, f"❌ DashScope SDK调用失败: {response.message}"

            except ImportError:
                return False, "❌ 未安装dashscope SDK，请运行: pip install dashscope"
            except Exception as e:
                # 如果SDK失败，尝试HTTP测试
                return await self._test_qwen_http_connection()

        except Exception as e:
            return False, f"❌ 阿里云模型连接测试失败: {str(e)}"

    async def _test_qwen_http_connection(self) -> tuple[bool, str]:
        """测试阿里云HTTP连接"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_keys['dashscope']}",
                "Content-Type": "application/json"
            }

            # 简单的文本生成测试
            data = {
                "model": "qwen-max",
                "messages": [{"role": "user", "content": "测试"}],
                "max_tokens": 5
            }

            response = self.session.post(
                "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=10,
                proxies={}
            )

            if response.status_code == 200:
                return True, "✅ 阿里云HTTP API连接正常"
            else:
                error_detail = ""
                try:
                    error_info = response.json()
                    error_detail = error_info.get('error', {}).get('message', '')
                except:
                    error_detail = response.text[:100]

                return False, f"❌ HTTP API调用失败 ({response.status_code}): {error_detail}"

        except Exception as e:
            return False, f"❌ HTTP连接测试失败: {str(e)}"

    async def _test_lm_studio_connection(self) -> tuple[bool, str]:
        """测试LM Studio连接"""
        try:
            # 检查LM Studio服务状态
            try:
                response = self.session.get("http://127.0.0.1:1234/v1/models", timeout=5)
                if response.status_code != 200:
                    return False, "❌ LM Studio服务未运行或不可访问"
            except Exception as e:
                return False, f"❌ 无法连接到LM Studio: {str(e)}"

            # 获取可用模型列表
            models = response.json()
            model_list = [model.get('id', '') for model in models.get('data', [])]

            if not model_list:
                return False, "❌ LM Studio未加载任何模型"

            # 检查是否有视觉语言模型
            vision_models = [m for m in model_list if 'vl' in m.lower() or 'vision' in m.lower()]

            if not vision_models:
                return False, f"❌ 未找到视觉语言模型，当前模型: {', '.join(model_list[:3])}"

            # 尝试简单的文本生成测试
            test_data = {
                "model": model_list[0],
                "messages": [{"role": "user", "content": "测试"}],
                "max_tokens": 5
            }

            test_response = self.session.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                json=test_data,
                timeout=10
            )

            if test_response.status_code == 200:
                return True, f"✅ LM Studio连接正常，可用视觉模型: {', '.join(vision_models)}"
            else:
                return False, f"❌ LM Studio模型调用失败 ({test_response.status_code})"

        except Exception as e:
            return False, f"❌ LM Studio连接测试失败: {str(e)}"

    async def _test_openai_connection(self) -> tuple[bool, str]:
        """测试OpenAI连接"""
        try:
            if not self.api_keys["openai"]:
                return False, "❌ 未设置OPENAI_API_KEY"

            headers = {
                "Authorization": f"Bearer {self.api_keys['openai']}",
                "Content-Type": "application/json"
            }

            # 简单的文本生成测试
            data = {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": "测试"}],
                "max_tokens": 5
            }

            response = self.session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=10
            )

            if response.status_code == 200:
                return True, "✅ OpenAI API连接正常"
            else:
                error_detail = ""
                try:
                    error_info = response.json()
                    error_detail = error_info.get('error', {}).get('message', '')
                except:
                    error_detail = response.text[:100]

                return False, f"❌ OpenAI API调用失败 ({response.status_code}): {error_detail}"

        except Exception as e:
            return False, f"❌ OpenAI连接测试失败: {str(e)}"

    def refresh_lm_studio_models(self) -> bool:
        """刷新LM Studio模型列表"""
        try:
            # 重置为基础模型
            self.supported_models = self.base_models.copy()

            # 获取LM Studio模型
            success, message, models = self.lm_studio_manager.refresh_models()

            if success and models:
                # 添加LM Studio模型到支持列表
                for model in models:
                    model_key = f"lm_studio_{model['id'].replace('/', '_').replace('.', '_').replace('-', '_')}"
                    model_name = f"LM Studio: {model['name']}"
                    self.supported_models[model_key] = model_name

                self.logger.info(f"成功刷新LM Studio模型: {len(models)} 个模型")
                return True
            else:
                self.logger.warning(f"LM Studio模型刷新失败: {message}")
                return False

        except Exception as e:
            self.logger.error(f"刷新LM Studio模型异常: {str(e)}")
            return False

    def get_lm_studio_models(self) -> List[Dict[str, str]]:
        """获取LM Studio模型列表"""
        try:
            return self.lm_studio_manager.get_vision_models()
        except Exception as e:
            self.logger.error(f"获取LM Studio模型失败: {str(e)}")
            return []

    def get_lm_studio_status(self) -> Dict[str, any]:
        """获取LM Studio状态"""
        try:
            return self.lm_studio_manager.get_server_status()
        except Exception as e:
            self.logger.error(f"获取LM Studio状态失败: {str(e)}")
            return {
                "running": False,
                "message": f"状态检查失败: {str(e)}",
                "models_count": 0,
                "vision_models_count": 0
            }

    def get_model_id_from_key(self, model_key: str) -> str:
        """从模型键获取实际的模型ID"""
        if model_key.startswith("lm_studio_"):
            # 获取LM Studio模型列表
            models = self.get_lm_studio_models()
            for model in models:
                # 重建模型键进行匹配
                expected_key = f"lm_studio_{model['id'].replace('/', '_').replace('.', '_').replace('-', '_')}"
                if expected_key == model_key:
                    return model['id']

            # 如果没有找到匹配，尝试从键名反推
            model_id = model_key.replace("lm_studio_", "").replace("_", "-")
            return model_id

        return model_key
