#!/usr/bin/env python3
"""
AI模型处理器 - 支持多种AI模型API进行图像识别
替代浏览器自动化方案
"""

import asyncio
import base64
import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
import requests
import time
import urllib3
from .log_manager import get_logger
from .lm_studio_manager import LMStudioManager

# 禁用SSL警告（用于解决SSL问题）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

try:
    import openai
except ImportError:
    openai = None

try:
    import anthropic
except ImportError:
    anthropic = None

try:
    import google.generativeai as genai
except ImportError:
    genai = None

from .config import Config

class AIModelProcessor:
    """AI模型处理器"""
    
    def __init__(self):
        self.logger = get_logger('AIModelProcessor')
        self.config = Config()

        # 初始化LM Studio管理器
        self.lm_studio_manager = LMStudioManager()

        # 基础模型支持
        self.base_models = {
            "qwen_qvq_max": "阿里通义千问-QVQ-Max"
        }

        # 动态支持的模型（包含LM Studio模型）
        self.supported_models = self.base_models.copy()

        # 从环境变量加载API密钥
        self.api_keys = {
            "dashscope": os.getenv("DASHSCOPE_API_KEY")  # 阿里云百炼API密钥
        }

        # 配置网络请求session
        self.session = requests.Session()
        self._configure_session()

        # 初始化时刷新LM Studio模型列表
        self.refresh_lm_studio_models()

        self.logger.info("AI模型处理器初始化完成")
    
    def _configure_session(self):
        """配置网络请求session，处理代理和SSL问题"""
        # 配置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 检查并处理代理设置
        # 移除可能导致问题的代理设置
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        if 'HTTP_PROXY' in os.environ:
            del os.environ['HTTP_PROXY']
        if 'HTTPS_PROXY' in os.environ:
            del os.environ['HTTPS_PROXY']
            
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # SSL配置 - 对于有SSL问题的情况，可以临时禁用SSL验证
        # 注意：这仅用于调试，生产环境建议修复SSL配置
        self.session.verify = True  # 默认启用SSL验证
        
    def _make_request_with_retry(self, url, **kwargs):
        """带重试的网络请求"""
        max_retries = 3
        backoff_factor = 2
        
        for attempt in range(max_retries):
            try:
                # 如果SSL验证失败，尝试禁用SSL验证
                if attempt > 0:
                    kwargs['verify'] = False
                    print(f"第{attempt + 1}次重试，尝试禁用SSL验证...")
                
                response = self.session.request(**kwargs, url=url)
                return response
            except requests.exceptions.SSLError as e:
                print(f"SSL错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except requests.exceptions.ProxyError as e:
                print(f"代理错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                # 移除代理设置
                if 'proxies' in kwargs:
                    del kwargs['proxies']
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except requests.exceptions.ConnectionError as e:
                print(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except Exception as e:
                print(f"请求错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
        
        return None
    
    def _initialize_clients(self):
        """初始化AI模型客户端 - 简化版本"""
        # 不需要初始化客户端，直接使用requests调用API
        pass
    
    def get_available_models(self) -> Dict[str, str]:
        """获取可用的模型列表 - 简化版本"""
        available = {}

        # 阿里云通义千问-QVQ-Max (如果有API密钥)
        if self.api_keys["dashscope"]:
            available["qwen_qvq_max"] = self.supported_models["qwen_qvq_max"]

        # LM Studio模型 (检查是否运行且有模型)
        if self.lm_studio_manager.is_server_running():
            # 刷新模型列表
            self.refresh_lm_studio_models()

            # 添加所有LM Studio模型
            for model_key, model_name in self.supported_models.items():
                if model_key.startswith("lm_studio_"):
                    available[model_key] = model_name

        return available

    def reload_api_keys(self):
        """重新加载API密钥"""
        self.api_keys = {
            "dashscope": os.getenv("DASHSCOPE_API_KEY")
        }

    def _check_lm_studio_qwen_available(self) -> bool:
        """检查LM Studio是否运行且有qwen2.5-vl-7b模型（保持向后兼容）"""
        try:
            return self.lm_studio_manager.is_server_running() and len(self.lm_studio_manager.get_vision_models()) > 0
        except Exception:
            return False
    

    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def _compress_image_base64(self, image_path: str, max_size: int = 5 * 1024 * 1024) -> str:
        """压缩图片并编码为base64"""
        try:
            from PIL import Image
            import io

            # 打开图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 计算压缩比例
                original_size = img.size
                quality = 85

                while True:
                    # 创建内存缓冲区
                    buffer = io.BytesIO()

                    # 保存压缩图片
                    img.save(buffer, format='JPEG', quality=quality, optimize=True)

                    # 检查大小
                    buffer_size = buffer.tell()

                    if buffer_size <= max_size or quality <= 20:
                        break

                    # 降低质量
                    quality -= 10
                    buffer.seek(0)
                    buffer.truncate()

                # 编码为base64
                buffer.seek(0)
                return base64.b64encode(buffer.read()).decode('utf-8')

        except Exception as e:
            print(f"图片压缩失败: {e}")
            # 如果压缩失败，返回原始图片
            return self._encode_image_to_base64(image_path)
    


    async def process_image_with_lm_studio(self, image_path: str, model_name: str = "monkeyocr-recognition", volume_code: str = "", chapter_codes: str = "") -> Optional[str]:
        """使用LM Studio模型处理图片"""
        try:
            base64_image = self._encode_image_to_base64(image_path)

            headers = {
                "Content-Type": "application/json"
            }

            # 构建包含分册章节信息的提示词
            prompt_text = self.config.QUOTA_EXTRACTION_PROMPT

            # 检查是否为Gemma模型，如果是则使用优化的提示词
            if "gemma" in model_name.lower():
                prompt_text = self._get_gemma_optimized_prompt(volume_code, chapter_codes)
            if volume_code and chapter_codes:
                chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
                if chapters:
                    volume_chapter_instruction = f"""

**🔥 重要：分册章节编号规则**：
- 本次识别的分册编号为：{volume_code}
- 章节编号包括：{', '.join(chapters)}
- 对于识别出的每个定额编号，必须按以下规则添加前缀：
  * 原编号"1-1"应输出为："{volume_code}-{chapters[0]}-1-1"
  * 原编号"1-2"应输出为："{volume_code}-{chapters[0]}-1-2"
  * 如果表格涉及多个章节，请根据内容判断属于哪个章节
  * 格式：分册编号-章节编号-原定额编号
- 示例：如果识别到定额编号"1-1"，输出应为"{volume_code}-{chapters[0]}-1-1"
"""
                    prompt_text = prompt_text + volume_chapter_instruction

            data = {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt_text
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.1
            }

            response = requests.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()

                # 检查响应格式
                if "choices" in result and len(result["choices"]) > 0:
                    choice = result["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                        if content and content.strip():
                            self.logger.info(f"✅ LM Studio模型 {model_name} 响应成功")

                            # 验证和修复JSON格式
                            cleaned_content = self._validate_and_fix_json_response(content, model_name)
                            return cleaned_content
                        else:
                            self.logger.error(f"❌ LM Studio模型 {model_name} 返回空内容")
                            return None
                    else:
                        self.logger.error(f"❌ LM Studio模型 {model_name} 响应格式错误: 缺少message.content")
                        self.logger.error(f"   响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        return None
                else:
                    self.logger.error(f"❌ LM Studio模型 {model_name} 响应格式错误: 缺少choices")
                    self.logger.error(f"   响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return None
            else:
                self.logger.error(f"❌ LM Studio模型 {model_name} 请求失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    self.logger.error(f"   错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
                except:
                    self.logger.error(f"   响应内容: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"❌ LM Studio模型 {model_name} 处理异常: {str(e)}")
            import traceback
            self.logger.error(f"   异常详情: {traceback.format_exc()}")
            return None



    async def process_image_with_qwen_qvq(self, image_path: str, model_name: str = "qvq-max", volume_code: str = "", chapter_codes: str = "") -> Optional[str]:
        """
        使用阿里云通义千问-QVQ模型处理图片

        Args:
            image_path: 图片文件路径
            model_name: 模型名称 (qvq-max 或 qvq-plus)

        Returns:
            Optional[str]: 识别结果
        """
        try:
            if not self.api_keys["dashscope"]:
                return None

            # 将图片编码为base64
            base64_image = self._encode_image_to_base64(image_path)

            # 构建请求数据 - 使用OpenAI兼容格式
            headers = {
                "Authorization": f"Bearer {self.api_keys['dashscope']}",
                "Content-Type": "application/json"
            }

            # 构建分册章节编号指令
            volume_chapter_instruction = ""
            if volume_code and chapter_codes:
                chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
                if chapters:
                    volume_chapter_instruction = f"""

**🔥 重要：分册章节编号规则**：
- 本次识别的分册编号为：{volume_code}
- 章节编号包括：{', '.join(chapters)}
- 对于识别出的每个定额编号，必须按以下规则添加前缀：
  * 原编号"1-1"应输出为："{volume_code}-{chapters[0]}-1-1"
  * 原编号"1-2"应输出为："{volume_code}-{chapters[0]}-1-2"
  * 如果表格涉及多个章节，请根据内容判断属于哪个章节
  * 格式：分册编号-章节编号-原定额编号
- 示例：如果识别到定额编号"1-1"，输出应为"{volume_code}-{chapters[0]}-1-1"
"""

            # QVQ模型的特殊提示词，针对定额表格优化
            qvq_prompt = f"""
请仔细分析这张北京市消耗定额表格图片。我需要你按照以下步骤进行分析：

1. **识别表格结构**：
   - 确定这是否为标准的北京市消耗定额表格
   - 识别表格上半部分的定额编号列（如：1-11、1-12、1-13）
   - 识别表格下半部分的资源消耗明细

2. **提取每个定额项的信息**（表格上半部分）：
   - 定额编号（如：1-1、1-2、1-3）{volume_chapter_instruction}
   - 定额项名称：需要组合主项名称和差异描述
     * 主项名称（如：人工挖一般土方）
     * 差异描述（如：一、二类土、三类土、四类土）
     * 完整名称（如：人工挖一般土方 一、二类土）
   - 工作内容（如：挖土、余土清理、修整底边等）
   - 单位（如：m³）

3. **提取资源消耗信息**（表格下半部分的每一行）：
   - 资源编号（如：00010701）
   - 类别（如：人工、机械、其他费用）
   - 子项名称（如：综合用工三类、电动打钉机、其他机具费占人工费）
   - 单位（如：工日、台班、%）
   - 消耗量（如：0.200、0.0039、1.50）

4. **特别注意**：
   - 图片中可能有多个定额项（如1-1、1-2、1-3），每个都要单独提取
   - 定额项名称要完整：主项名称 + 差异描述（如：人工挖一般土方 一、二类土）
   - 仔细观察表格中每列的差异描述文字，将其合并到定额项名称中
   - 每个定额项通常有相同的资源消耗明细，但消耗量可能不同
   - 单位为"%"的资源项是特殊的百分比费用项
   - 确保提取所有资源消耗行
   - 保留消耗量的小数精度

请以JSON格式返回结果（支持多个定额项）：
{{
    "quotas": [
        {{
            "parent_quota": {{
                "code": "定额编号",
                "name": "定额项名称",
                "work_content": "工作内容",
                "unit": "单位"
            }},
            "resource_consumption": [
                {{
                    "resource_code": "资源编号",
                    "category": "类别",
                    "name": "子项名称",
                    "unit": "单位",
                    "consumption": "消耗量"
                }}
            ]
        }}
    ]
}}

如果只有一个定额项，quotas数组中只包含一个元素。
如果有多个定额项（如1-11、1-12、1-13），quotas数组中包含多个元素。
"""

            # 模型回退策略 - 如果QVQ模型不可用，使用通用模型
            fallback_models = [model_name, "qwen-vl-max", "qwen-max", "qwen-vl-plus"]
            
            for try_model in fallback_models:
                print(f"尝试使用模型: {try_model}")
                
                data = {
                    "model": try_model,
                    "messages": [
                        {
                            "role": "user", 
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{base64_image}"
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": qvq_prompt
                                }
                            ]
                        }
                    ],
                    "stream": True if try_model.startswith("qvq") else False  # QVQ模型支持流式，其他不一定
                }

                # 发送请求到阿里云百炼API - 使用重试机制
                response = self._make_request_with_retry(
                    "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                    method='POST',
                    headers=headers,
                    json=data,
                    stream=data["stream"],
                    timeout=120
                )

                if response.status_code == 200:
                    print(f"✅ 模型 {try_model} 连接成功")
                    break
                else:
                    error_text = response.text if hasattr(response, 'text') else str(response)
                    print(f"❌ 模型 {try_model} 失败: {response.status_code} - {error_text[:100]}")
                    if try_model == fallback_models[-1]:  # 最后一个模型也失败了
                        print(f"所有模型都失败了")
                        return None
                    continue
            else:
                print(f"所有模型都失败了") 
                return None

            # 处理响应 - 支持流式和非流式
            if data["stream"]:
                # 处理流式响应 (QVQ模型)
                reasoning_content = ""  # 思考过程
                answer_content = ""     # 最终回答
                is_answering = False    # 是否开始回答阶段

                for line in response.iter_lines():
                    if line:
                        line_text = line.decode('utf-8')
                        if line_text.startswith('data: '):
                            data_text = line_text[6:]  # 移除 'data: ' 前缀

                            if data_text.strip() == '[DONE]':
                                break

                            try:
                                chunk_data = json.loads(data_text)
                                if 'choices' in chunk_data and chunk_data['choices']:
                                    delta = chunk_data['choices'][0].get('delta', {})

                                    # 处理思考过程
                                    if 'reasoning_content' in delta and delta['reasoning_content']:
                                        reasoning_content += delta['reasoning_content']

                                    # 处理最终回答
                                    elif 'content' in delta and delta['content']:
                                        if not is_answering:
                                            is_answering = True
                                        answer_content += delta['content']

                            except json.JSONDecodeError:
                                continue

                # 返回最终回答内容
                if answer_content:
                    print(f"思考过程长度: {len(reasoning_content)} 字符")
                    print(f"回答内容长度: {len(answer_content)} 字符")
                    return answer_content
                else:
                    print("流式模型未返回有效回答")
                    return None
            else:
                # 处理非流式响应 (通用模型)
                try:
                    result = response.json()
                    if 'choices' in result and result['choices']:
                        content = result['choices'][0]['message']['content']
                        print(f"回答内容长度: {len(content)} 字符")
                        return content
                    else:
                        print("非流式模型未返回有效回答")
                        return None
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    return None

        except Exception as e:
            print(f"QVQ模型处理失败: {e}")
            return None
    
    async def process_image(self, image_path: str, model_type: str, **kwargs) -> Optional[str]:
        """
        使用指定模型处理图片 - 简化版本

        Args:
            image_path: 图片路径
            model_type: 模型类型
            **kwargs: 额外参数（包含volume_code和chapter_codes）

        Returns:
            Optional[str]: 识别结果
        """
        print(f"使用 {self.supported_models.get(model_type, model_type)} 处理图片: {image_path}")

        # 提取分册章节信息
        volume_code = kwargs.get('volume_code', '')
        chapter_codes = kwargs.get('chapter_codes', '')

        try:
            if model_type == "qwen_qvq_max":
                return await self.process_image_with_qwen_qvq(image_path, "qvq-max", volume_code, chapter_codes)
            elif model_type.startswith("lm_studio_"):
                # 获取实际的模型ID
                actual_model_id = self.get_model_id_from_key(model_type)
                return await self.process_image_with_lm_studio(image_path, actual_model_id, volume_code, chapter_codes)
            else:
                print(f"不支持的模型类型: {model_type}")
                return None

        except Exception as e:
            print(f"处理图片时发生错误: {e}")
            return None

    # 信息价识别功能已移至独立模块 intelligent_price_info_processor.py

    async def test_model_connection(self, model_type: str) -> tuple[bool, str]:
        """
        测试AI模型连接

        Args:
            model_type: 模型类型

        Returns:
            (是否成功, 状态消息)
        """
        try:
            if model_type == "qwen_qvq_max":
                return await self._test_qwen_qvq_connection()
            elif model_type.startswith("lm_studio_"):
                # 获取实际的模型ID并测试
                actual_model_id = self.get_model_id_from_key(model_type)
                success, message = self.test_lm_studio_model(actual_model_id)
                return success, message
            else:
                return False, f"不支持的模型类型: {model_type}"

        except Exception as e:
            return False, f"连接测试失败: {str(e)}"

    async def _test_qwen_qvq_connection(self) -> tuple[bool, str]:
        """测试阿里云QVQ模型连接"""
        try:
            if not self.api_keys["dashscope"]:
                return False, "❌ 未设置DASHSCOPE_API_KEY"

            # 尝试使用DashScope SDK
            try:
                import dashscope
                from dashscope import Generation

                dashscope.api_key = self.api_keys["dashscope"]

                # 简单的文本生成测试
                response = Generation.call(
                    model='qwen-max',
                    prompt='测试连接',
                    max_tokens=5
                )

                if response.status_code == 200:
                    return True, "✅ 阿里云DashScope SDK连接正常"
                else:
                    return False, f"❌ DashScope SDK调用失败: {response.message}"

            except ImportError:
                return False, "❌ 未安装dashscope SDK，请运行: pip install dashscope"
            except Exception as e:
                # 如果SDK失败，尝试HTTP测试
                return await self._test_qwen_http_connection()

        except Exception as e:
            return False, f"❌ 阿里云模型连接测试失败: {str(e)}"

    async def _test_qwen_http_connection(self) -> tuple[bool, str]:
        """测试阿里云HTTP连接"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_keys['dashscope']}",
                "Content-Type": "application/json"
            }

            # 简单的文本生成测试
            data = {
                "model": "qwen-max",
                "messages": [{"role": "user", "content": "测试"}],
                "max_tokens": 5
            }

            response = requests.post(
                "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=10,
                proxies={}
            )

            if response.status_code == 200:
                return True, "✅ 阿里云HTTP API连接正常"
            else:
                error_detail = ""
                try:
                    error_info = response.json()
                    error_detail = error_info.get('error', {}).get('message', '')
                except:
                    error_detail = response.text[:100]

                return False, f"❌ HTTP API调用失败 ({response.status_code}): {error_detail}"

        except Exception as e:
            return False, f"❌ HTTP连接测试失败: {str(e)}"

    async def _test_lm_studio_connection(self) -> tuple[bool, str]:
        """测试LM Studio连接"""
        try:
            # 检查LM Studio服务状态
            try:
                response = requests.get("http://127.0.0.1:1234/v1/models", timeout=5)
                if response.status_code != 200:
                    return False, "❌ LM Studio服务未运行或不可访问"
            except Exception as e:
                return False, f"❌ 无法连接到LM Studio: {str(e)}"

            # 获取可用模型列表
            models = response.json()
            model_list = [model.get('id', '') for model in models.get('data', [])]

            if not model_list:
                return False, "❌ LM Studio未加载任何模型"

            # 检查是否有视觉语言模型
            vision_models = [m for m in model_list if 'vl' in m.lower() or 'vision' in m.lower()]

            if not vision_models:
                return False, f"❌ 未找到视觉语言模型，当前模型: {', '.join(model_list[:3])}"

            # 尝试简单的文本生成测试
            test_data = {
                "model": model_list[0],
                "messages": [{"role": "user", "content": "测试"}],
                "max_tokens": 5
            }

            test_response = requests.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                json=test_data,
                timeout=10
            )

            if test_response.status_code == 200:
                return True, f"✅ LM Studio连接正常，可用视觉模型: {', '.join(vision_models)}"
            else:
                return False, f"❌ LM Studio模型调用失败 ({test_response.status_code})"

        except Exception as e:
            return False, f"❌ LM Studio连接测试失败: {str(e)}"

    def refresh_lm_studio_models(self) -> bool:
        """刷新LM Studio模型列表"""
        try:
            # 重置为基础模型
            self.supported_models = self.base_models.copy()

            # 获取LM Studio模型
            success, message, models = self.lm_studio_manager.refresh_models()

            if success and models:
                # 添加LM Studio模型到支持列表
                for model in models:
                    model_key = f"lm_studio_{model['id'].replace('/', '_').replace('.', '_').replace('-', '_')}"
                    model_name = f"LM Studio: {model['name']}"
                    self.supported_models[model_key] = model_name

                self.logger.info(f"成功刷新LM Studio模型: {len(models)} 个模型")
                return True
            else:
                self.logger.warning(f"LM Studio模型刷新失败: {message}")
                return False

        except Exception as e:
            self.logger.error(f"刷新LM Studio模型异常: {str(e)}")
            return False

    def get_lm_studio_models(self) -> List[Dict[str, str]]:
        """获取LM Studio模型列表"""
        try:
            return self.lm_studio_manager.get_vision_models()
        except Exception as e:
            self.logger.error(f"获取LM Studio模型失败: {str(e)}")
            return []

    def get_lm_studio_status(self) -> Dict[str, any]:
        """获取LM Studio状态"""
        try:
            return self.lm_studio_manager.get_server_status()
        except Exception as e:
            self.logger.error(f"获取LM Studio状态失败: {str(e)}")
            return {
                "running": False,
                "message": f"状态检查失败: {str(e)}",
                "models_count": 0,
                "vision_models_count": 0
            }

    def test_lm_studio_model(self, model_id: str) -> tuple[bool, str]:
        """测试特定的LM Studio模型"""
        try:
            return self.lm_studio_manager.test_model_connection(model_id)
        except Exception as e:
            return False, f"模型测试失败: {str(e)}"

    def get_model_id_from_key(self, model_key: str) -> str:
        """从模型键获取实际的模型ID"""
        if model_key.startswith("lm_studio_"):
            # 获取LM Studio模型列表
            models = self.get_lm_studio_models()
            for model in models:
                # 重建模型键进行匹配
                expected_key = f"lm_studio_{model['id'].replace('/', '_').replace('.', '_').replace('-', '_')}"
                if expected_key == model_key:
                    return model['id']

            # 如果没有找到匹配，尝试从键名反推
            model_id = model_key.replace("lm_studio_", "").replace("_", "-")
            return model_id

        return model_key

    def _validate_and_fix_json_response(self, content: str, model_name: str) -> str:
        """验证和修复JSON响应格式"""
        try:
            # 首先尝试直接解析
            json.loads(content)
            self.logger.info(f"✅ 模型 {model_name} 返回有效JSON格式")
            return content
        except json.JSONDecodeError:
            self.logger.warning(f"⚠️ 模型 {model_name} 返回的JSON格式有问题，尝试修复...")

            # 尝试提取JSON部分
            try:
                # 查找第一个 { 和最后一个 }
                start = content.find('{')
                end = content.rfind('}')

                if start != -1 and end != -1 and end > start:
                    json_part = content[start:end+1]

                    # 尝试解析提取的部分
                    json.loads(json_part)
                    self.logger.info(f"✅ 成功从模型 {model_name} 响应中提取有效JSON")
                    return json_part
                else:
                    self.logger.error(f"❌ 无法从模型 {model_name} 响应中找到JSON结构")

            except json.JSONDecodeError:
                self.logger.error(f"❌ 提取的JSON仍然无效")

            # 如果JSON修复失败，尝试其他修复策略
            try:
                # 移除可能的markdown代码块标记
                cleaned = content.replace('```json', '').replace('```', '').strip()

                # 再次尝试提取JSON
                start = cleaned.find('{')
                end = cleaned.rfind('}')

                if start != -1 and end != -1 and end > start:
                    json_part = cleaned[start:end+1]
                    json.loads(json_part)
                    self.logger.info(f"✅ 清理markdown后成功提取JSON")
                    return json_part

            except json.JSONDecodeError:
                pass

            # 如果所有修复尝试都失败，返回原始内容并记录警告
            self.logger.warning(f"⚠️ 无法修复模型 {model_name} 的JSON格式，返回原始内容")
            self.logger.warning(f"   原始内容预览: {content[:200]}...")
            return content

    def _get_gemma_optimized_prompt(self, volume_code: str = "", chapter_codes: str = "") -> str:
        """获取针对Google Gemma模型优化的提示词"""

        # Gemma模型优化的提示词，更加结构化和清晰
        base_prompt = """你是一个专业的建筑工程定额数据提取专家。请仔细分析这张图片中的定额表格，并按照以下要求提取信息：

## 📋 任务目标
从图片中的定额表格提取完整的定额数据，包括定额编号、项目名称、单位、人工、材料、机械等信息。

## 🎯 输出格式要求
请严格按照以下JSON格式输出，确保数据准确完整：

```json
{
  "定额数据": [
    {
      "定额编号": "具体编号",
      "项目名称": "具体名称",
      "计量单位": "具体单位",
      "人工费": "数值",
      "材料费": "数值",
      "机械费": "数值",
      "综合单价": "数值",
      "其他费用": "数值(如有)",
      "备注": "备注信息(如有)"
    }
  ]
}
```

## ⚠️ 重要注意事项
1. **数据准确性**: 确保所有数字和文字都准确提取，不要遗漏或错误
2. **格式统一**: 严格按照JSON格式输出，便于程序解析
3. **完整性**: 提取表格中的所有定额项目，不要遗漏任何行
4. **单位规范**: 保持原始单位格式，如m³、m²、t等
5. **数值处理**: 数值保持原始格式，包括小数点

## 🔍 识别重点
- 定额编号通常在表格第一列
- 项目名称描述工程内容
- 计量单位如m、m²、m³、t、kg等
- 费用数据通常为数字，可能包含小数点
- 注意表格的表头和数据行的对应关系"""

        # 如果提供了分册和章节信息，添加编号规则
        if volume_code and chapter_codes:
            chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
            if chapters:
                volume_chapter_instruction = f"""

## 🔥 特别重要：编号前缀规则
- **分册编号**: {volume_code}
- **章节编号**: {', '.join(chapters)}
- **编号格式**: 分册编号-章节编号-原定额编号

### 编号转换示例：
- 原编号 "1-1" → 输出 "{volume_code}-{chapters[0]}-1-1"
- 原编号 "1-2" → 输出 "{volume_code}-{chapters[0]}-1-2"
- 原编号 "2-1" → 输出 "{volume_code}-{chapters[0]}-2-1"

**请确保所有定额编号都按此规则添加前缀！**"""
                base_prompt += volume_chapter_instruction

        base_prompt += """

## 🚀 开始分析
请现在开始分析图片中的定额表格，按照上述要求提取数据并输出JSON格式结果。"""

        return base_prompt
