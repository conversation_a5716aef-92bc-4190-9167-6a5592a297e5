#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试当前状态脚本
检查数据库连接和识别定额修订的当前状态
"""

def debug_current_status():
    """调试当前状态"""
    try:
        print("🔍 调试当前状态...")
        
        from src.advanced_quota_manager import AdvancedQuotaManager
        from src.config_persistence_manager import ConfigPersistenceManager
        
        # 1. 检查配置
        print("\n📊 检查配置...")
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        print(f"   数据库类型: {quota_db_config.get('db_type')}")
        print(f"   数据库名称: {quota_db_config.get('db_name')}")
        print(f"   主机: {quota_db_config.get('host')}")
        print(f"   端口: {quota_db_config.get('port')}")
        print(f"   用户: {quota_db_config.get('username')}")
        
        # 2. 检查高级定额管理器
        print("\n🔍 检查高级定额管理器...")
        quota_manager = AdvancedQuotaManager()
        
        print(f"   管理器存在: {quota_manager is not None}")
        print(f"   连接对象: {hasattr(quota_manager, 'connection') and quota_manager.connection is not None}")
        
        if hasattr(quota_manager, 'connection') and quota_manager.connection:
            print(f"   数据库路径: {getattr(quota_manager, 'db_path', '未知')}")
            print(f"   数据库类型: {getattr(quota_manager, 'db_type', '未知')}")
            
            # 测试连接
            try:
                cursor = quota_manager.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                print(f"   连接测试: ✅ 正常")
            except Exception as e:
                print(f"   连接测试: ❌ 失败 - {e}")
        else:
            print(f"   连接状态: ❌ 未连接")
        
        # 3. 检查修订处理器
        print("\n🔍 检查修订处理器...")
        if quota_manager.revision_processor:
            print(f"   修订处理器存在: ✅")
            print(f"   quota_manager引用: {quota_manager.revision_processor.quota_manager is not None}")
            
            # 测试数据加载
            try:
                success, message, quota_data, stats = quota_manager.revision_processor.load_from_connected_database()
                print(f"   数据加载测试: {'✅' if success else '❌'} - {message}")
                if success:
                    print(f"   数据统计: {stats}")
            except Exception as e:
                print(f"   数据加载测试: ❌ 异常 - {e}")
        else:
            print(f"   修订处理器存在: ❌")
        
        # 4. 直接测试数据库连接
        print("\n🔍 直接测试数据库连接...")
        try:
            import psycopg2
            
            conn = psycopg2.connect(
                host=quota_db_config.get('host', 'localhost'),
                port=int(quota_db_config.get('port', 5432)),
                user=quota_db_config.get('username', 'postgres'),
                password=quota_db_config.get('password', ''),
                database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
                client_encoding='utf8'
            )
            
            cursor = conn.cursor()
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            print(f"   直接连接: ✅ 成功")
            print(f"   数据库表: {tables}")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"   直接连接: ❌ 失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

if __name__ == "__main__":
    debug_current_status()
