#!/usr/bin/env python3
"""
北京市消耗定额智能提取系统
主程序 - Gradio Web界面
"""

import os
import warnings

# 设置环境变量，禁用不必要的功能和错误
os.environ['GRADIO_ANALYTICS_ENABLED'] = 'False'
os.environ['DISABLE_TELEMETRY'] = 'True'
os.environ['HF_HUB_DISABLE_TELEMETRY'] = 'True'
os.environ['HUGGINGFACE_HUB_DISABLE_TELEMETRY'] = 'True'

# 禁用警告
warnings.filterwarnings("ignore")

import gradio as gr
import pandas as pd
import asyncio
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import tempfile
import shutil

from src.pdf_processor import PDFProcessor
from src.ai_model_processor import AIModelProcessor
from src.data_processor import DataProcessor
from src.config import Config
from src.log_manager import get_log_manager, get_logger
from src.pdf_storage_manager import PDFStorageManager
from src.pdf_management_interface import PDFManagementInterface

# 导入高级定额管理组件
try:
    from src.advanced_quota_interface import AdvancedQuotaInterface
    from src.advanced_quota_handlers import AdvancedQuotaHandlers
except ImportError:
    print("警告: 无法导入高级定额管理组件，某些功能可能不可用")
    AdvancedQuotaInterface = None
    AdvancedQuotaHandlers = None

class QuotaExtractionApp:
    """定额提取应用主类"""
    
    def __init__(self):
        # 初始化日志管理器
        self.log_manager = get_log_manager()
        self.logger = get_logger('MainApp')

        self.config = Config()
        self.pdf_processor = PDFProcessor()
        self.ai_processor = AIModelProcessor()
        self.data_processor = DataProcessor()
        self.pdf_storage = PDFStorageManager()
        self.pdf_management = PDFManagementInterface()

        self.logger.info("定额创建工具主应用初始化完成")

        # 初始化高级定额管理组件
        if AdvancedQuotaInterface and AdvancedQuotaHandlers:
            self.advanced_quota_interface = AdvancedQuotaInterface()
            self.advanced_quota_handlers = AdvancedQuotaHandlers()
        else:
            self.advanced_quota_interface = None
            self.advanced_quota_handlers = None
        
    async def process_pdf(
        self,
        pdf_file: str,
        start_page: int,
        end_page: int,
        model_type: str,
        volume_code: str,
        chapter_codes: str,
        progress: gr.Progress
    ) -> Tuple[str, str, List[Dict]]:
        """
        处理PDF文件并提取定额信息

        Args:
            pdf_file: PDF文件路径
            start_page: 起始页码
            end_page: 结束页码
            model_type: AI模型类型
            volume_code: 分册编号
            chapter_codes: 章节编号（逗号分隔）
            progress: Gradio进度条

        Returns:
            Tuple[str, str, List[Dict]]: (CSV文件路径, 处理日志, 处理详情)
        """
        try:
            progress(0, desc="开始处理PDF文件...")
            processing_details = []

            # 1. 提取PDF页面为图片
            progress(0.1, desc="提取PDF页面为图片...")
            images = await self.pdf_processor.extract_pages_as_images(
                pdf_file, start_page, end_page
            )

            total_pages = len(images)
            processed_data = []

            # 2. 逐页处理图片
            for i, image_path in enumerate(images):
                current_page = start_page + i
                progress(
                    0.1 + (i / total_pages) * 0.7,
                    desc=f"使用AI模型处理第 {current_page} 页 ({i+1}/{total_pages})..."
                )

                page_detail = {
                    "page": current_page,
                    "image_path": image_path,
                    "status": "处理中",
                    "model": model_type,
                    "start_time": time.time()
                }

                # 使用AI模型处理图片，传递分册章节信息
                recognition_result = await self.ai_processor.process_image(
                    image_path, model_type,
                    volume_code=volume_code,
                    chapter_codes=chapter_codes
                )

                page_detail["end_time"] = time.time()
                page_detail["duration"] = page_detail["end_time"] - page_detail["start_time"]

                if recognition_result:
                    page_detail["status"] = "识别成功"
                    page_detail["result_length"] = len(recognition_result)

                    # 解析识别结果
                    page_data = self.data_processor.parse_recognition_result(
                        recognition_result, current_page
                    )

                    if page_data:
                        processed_data.extend(page_data)
                        page_detail["extracted_items"] = len(page_data)
                    else:
                        page_detail["extracted_items"] = 0
                        page_detail["status"] = "解析失败"
                else:
                    page_detail["status"] = "识别失败"
                    page_detail["extracted_items"] = 0

                processing_details.append(page_detail)

                # 清理临时图片文件
                try:
                    os.remove(image_path)
                except:
                    pass

            # 3. 生成CSV文件
            progress(0.8, desc="生成CSV文件...")
            csv_path = self.data_processor.generate_csv(processed_data, volume_code, chapter_codes)

            progress(1.0, desc="处理完成!")

            # 生成处理报告
            successful_pages = len([d for d in processing_details if d["status"] == "识别成功"])
            total_items = sum([d.get("extracted_items", 0) for d in processing_details])

            log_message = f"""处理完成！
📊 处理统计:
- 总页数: {total_pages}
- 成功页数: {successful_pages}
- 失败页数: {total_pages - successful_pages}
- 提取定额项: {total_items}
- 使用模型: {self.ai_processor.supported_models.get(model_type, model_type)}
"""

            return csv_path, log_message, processing_details

        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            return None, error_msg, []
    
    def create_interface(self) -> gr.Interface:
        """创建Gradio界面"""
        
        # 创建自定义CSS样式
        custom_css = """
        /* 解决字体404错误 */
        @font-face {
            font-family: 'ui-sans-serif';
            src: local('Arial'), local('Helvetica'), local('sans-serif');
        }
        @font-face {
            font-family: 'system-ui';
            src: local('Arial'), local('Helvetica'), local('sans-serif');
        }

        /* 全局样式 */
        .gradio-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
        }

        /* 隐藏错误信息和警告 */
        .error-message, .warning-message, .toast-error {
            display: none !important;
        }

        /* 日志显示区域样式 */
        .log-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .log-display {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .log-display::-webkit-scrollbar {
            width: 8px;
        }

        .log-display::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .log-display::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .log-display::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .log-stats {
            margin-top: 10px;
        }

        /* 主容器样式 */
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 标题样式 */
        .main-title {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            font-size: 2.8em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: 2px;
        }

        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        /* 功能卡片样式 */
        .feature-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        /* 按钮样式增强 */
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            border: none !important;
            border-radius: 25px !important;
            color: white !important;
            padding: 12px 30px !important;
            font-weight: bold !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
        }

        .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c) !important;
            border: none !important;
            border-radius: 25px !important;
            color: white !important;
            padding: 10px 25px !important;
            font-weight: bold !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4) !important;
        }

        .btn-secondary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.6) !important;
        }

        /* 文件上传区域美化 */
        .upload-area {
            border: 2px dashed #667eea !important;
            border-radius: 15px !important;
            padding: 40px !important;
            text-align: center !important;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
        }

        .upload-area:hover {
            border-color: #764ba2 !important;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2)) !important;
            transform: scale(1.02) !important;
        }

        /* 状态指示器 */
        .status-success {
            background: linear-gradient(45deg, #4facfe, #00f2fe) !important;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 20px !important;
            font-weight: bold !important;
            text-align: center !important;
            margin: 10px 0 !important;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3) !important;
        }

        .status-warning {
            background: linear-gradient(45deg, #fa709a, #fee140) !important;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 20px !important;
            font-weight: bold !important;
            text-align: center !important;
            margin: 10px 0 !important;
            box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3) !important;
        }

        .status-error {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
            color: white !important;
            padding: 10px 20px !important;
            border-radius: 20px !important;
            font-weight: bold !important;
            text-align: center !important;
            margin: 10px 0 !important;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3) !important;
        }

        /* 分隔线美化 */
        .divider {
            height: 3px !important;
            background: linear-gradient(90deg, transparent, #667eea, #764ba2, transparent) !important;
            margin: 40px 0 !important;
            border: none !important;
            border-radius: 2px !important;
        }

        /* 档案管理区域 */
        .archive-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7)) !important;
            border-radius: 20px !important;
            padding: 30px !important;
            margin: 30px 0 !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
            border: 1px solid rgba(102, 126, 234, 0.2) !important;
        }

        /* 数据库转换区域 */
        .database-section {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
            border-radius: 20px !important;
            padding: 30px !important;
            margin: 30px 0 !important;
            border: 1px solid rgba(102, 126, 234, 0.2) !important;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1) !important;
        }

        /* 输入框美化 */
        .gr-textbox, .gr-dropdown, .gr-number {
            border-radius: 10px !important;
            border: 2px solid rgba(102, 126, 234, 0.2) !important;
            transition: all 0.2s ease !important;
        }

        .gr-textbox:focus, .gr-dropdown:focus, .gr-number:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        }

        /* 下拉菜单优化 */
        .gr-dropdown {
            cursor: pointer !important;
        }

        .gr-dropdown .wrap {
            transition: all 0.15s ease !important;
        }

        .gr-dropdown:hover {
            border-color: rgba(102, 126, 234, 0.4) !important;
        }

        /* 下拉菜单选项优化 */
        .gr-dropdown .option {
            transition: background-color 0.1s ease !important;
        }

        .gr-dropdown .option:hover {
            background-color: rgba(102, 126, 234, 0.1) !important;
        }

        /* 禁用某些动画以提高性能 */
        .gr-dropdown * {
            will-change: auto !important;
        }

        /* 优化下拉菜单的渲染性能 */
        .gr-dropdown .wrap {
            contain: layout style !important;
        }

        /* 预加载优化 */
        .gr-dropdown {
            transform: translateZ(0) !important;
        }
    </style>

    <script>
        // 下拉菜单响应性优化
        document.addEventListener('DOMContentLoaded', function() {
            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // 优化下拉菜单的事件处理
            const dropdowns = document.querySelectorAll('.gr-dropdown');
            dropdowns.forEach(dropdown => {
                // 减少重绘
                dropdown.style.willChange = 'auto';

                // 优化点击响应
                dropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                }, { passive: true });

                // 优化hover效果
                dropdown.addEventListener('mouseenter', function() {
                    this.style.borderColor = 'rgba(102, 126, 234, 0.4)';
                }, { passive: true });

                dropdown.addEventListener('mouseleave', function() {
                    this.style.borderColor = 'rgba(102, 126, 234, 0.2)';
                }, { passive: true });
            });
        });
    </script>

    <style>

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px !important;
                padding: 20px !important;
            }

            .main-title {
                font-size: 2em !important;
            }

            .feature-card {
                padding: 20px !important;
                margin: 10px 0 !important;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 图标样式 */
        .icon {
            font-size: 1.2em;
            margin-right: 8px;
            vertical-align: middle;
        }

        /* 进度条美化 */
        .progress-container {
            background: rgba(255, 255, 255, 0.2) !important;
            border-radius: 10px !important;
            padding: 3px !important;
            margin: 15px 0 !important;
        }

        .progress-bar {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            border-radius: 8px !important;
            height: 8px !important;
            transition: width 0.3s ease !important;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.5) !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            z-index: 1000 !important;
        }

        .modal-content {
            background: white !important;
            border-radius: 20px !important;
            padding: 30px !important;
            max-width: 600px !important;
            width: 90% !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
            animation: modalSlideIn 0.3s ease-out !important;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        """

        with gr.Blocks(
            title="🔧 北京市2021消耗定额创建工具 | Beijing Quota Creation Tool",
            theme=gr.themes.Soft(),
            css=custom_css
        ) as interface:

            # 主标题区域
            with gr.Row(elem_classes="main-container fade-in-up"):
                with gr.Column():
                    gr.HTML("""
                        <div class="main-title">
                            🔧 北京市2021消耗定额创建工具
                        </div>
                        <div class="subtitle">
                            <span class="icon">🚀</span>智能PDF识别
                            <span class="icon">📊</span>数据提取
                            <span class="icon">💰</span>价格计算
                            <span class="icon">🗄️</span>数据库转换
                            <span class="icon">📁</span>档案管理
                        </div>
                        <div style="text-align: center; margin: 20px 0; padding: 20px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 1px solid rgba(102, 126, 234, 0.2);">
                            <h3 style="color: #667eea; margin-bottom: 15px;">✨ 系统特色功能</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: left;">
                                <div>🤖 <strong>革新的精准AI定额数据识别技术</strong><br><small>最优在线和本地AI模型可选</small></div>
                                <div>💰 <strong>智能价格计算</strong><br><small>自动单价匹配</small></div>
                                <div>🗄️ <strong>定额数据库创建</strong><br><small>5种格式支持</small></div>
                                <div>📁 <strong>定额档案管理</strong><br><small>完整预览合并</small></div>
                            </div>
                        </div>
                        <hr class="divider">
                    """)

            # 主要功能区域
            with gr.Row():
                with gr.Column(scale=2):
                    # AI模型选择卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">🤖</span>AI模型选择
                            </h3>
                        """)

                        with gr.Row():
                            with gr.Column(scale=3):
                                model_dropdown = gr.Dropdown(
                                    label="🎯 选择AI识别模型",
                                    choices=[
                                        ("阿里通义千问-QVQ-Max (在线)", "qwen_qvq_max")
                                    ],
                                    value=None,
                                    info="💡 推荐使用LM Studio本地模型，稳定可靠",
                                    allow_custom_value=False,
                                    interactive=True,
                                    elem_id="model_dropdown"
                                )
                            with gr.Column(scale=1):
                                with gr.Row():
                                    refresh_models_btn = gr.Button(
                                        "🔄 刷新模型",
                                        elem_classes="btn-info",
                                        size="sm"
                                    )
                                    test_quota_model_btn = gr.Button(
                                        "🔧 测试连接",
                                        elem_classes="btn-secondary",
                                        size="sm"
                                    )

                        # 模型测试结果显示
                        quota_model_test_output = gr.HTML(
                            label="模型连接状态",
                            value="<p style='color: #666;'>点击'🔧 测试连接'检查模型状态</p>"
                        )

                        # LM Studio状态显示
                        lm_studio_status = gr.HTML(
                            value="<p style='color: #666;'>正在检查LM Studio状态...</p>",
                            label="LM Studio状态"
                        )

                        with gr.Row():
                            with gr.Column(scale=3):
                                api_key_input = gr.Textbox(
                                    label="🔑 API密钥 (仅千问QVQ-Max需要)",
                                    placeholder="请输入阿里云百炼API密钥",
                                    type="password",
                                    info="💡 本地模型无需API密钥"
                                )
                            with gr.Column(scale=1):
                                save_api_key_btn = gr.Button("💾 保存", elem_classes="btn-primary", size="sm")

                    # 文件上传卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📄</span>PDF文件选择
                            </h3>
                        """)

                        # PDF文件选择方式
                        with gr.Tabs():
                            with gr.Tab("📤 上传新文件"):
                                pdf_input = gr.File(
                                    label="选择PDF定额文件",
                                    file_types=[".pdf"],
                                    type="filepath",
                                    elem_classes="upload-area"
                                )

                            with gr.Tab("📁 选择已存储文件"):
                                with gr.Row():
                                    stored_pdf_dropdown = gr.Dropdown(
                                        label="选择已存储的定额PDF",
                                        choices=[],
                                        value=None,
                                        interactive=True,
                                        scale=3
                                    )
                                    refresh_stored_btn = gr.Button("🔄 刷新", elem_classes="btn-secondary", size="sm", scale=1)

                                stored_pdf_info = gr.HTML(
                                    value="<p style='color: #666; text-align: center;'>选择PDF文件查看详情</p>",
                                    elem_classes="file-info"
                                )

                    # PDF浏览器卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">🔍</span>PDF智能浏览器
                            </h3>
                        """)

                        # PDF浏览器控制
                        with gr.Row(visible=False) as pdf_controls:
                            with gr.Column(scale=1):
                                current_page = gr.Number(
                                    label="📍 当前页码",
                                    value=1,
                                    minimum=1,
                                    precision=0
                                )
                            with gr.Column(scale=1):
                                total_pages_display = gr.Textbox(
                                    label="📊 总页数",
                                    value="0",
                                    interactive=False
                                )
                            with gr.Column(scale=2):
                                with gr.Row():
                                    prev_page_btn = gr.Button("⬅️ 上一页", size="sm", elem_classes="btn-secondary")
                                    next_page_btn = gr.Button("➡️ 下一页", size="sm", elem_classes="btn-secondary")
                                    zoom_btn = gr.Button("🔍 放大查看", size="sm", elem_classes="btn-secondary")

                        # PDF页面显示
                        pdf_viewer = gr.HTML(
                            label="PDF预览",
                            value="""
                                <div style="text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                    <div style="font-size: 3em; margin-bottom: 20px;">📄</div>
                                    <h3 style="color: #667eea; margin-bottom: 10px;">PDF智能浏览器</h3>
                                    <p style="color: #666;">上传PDF文件后将显示交互式浏览器</p>
                                    <p style="color: #999; font-size: 0.9em;">支持页面导航、缩放查看、快速跳转</p>
                                </div>
                            """,
                            visible=True
                        )

                    # 隐藏状态变量：当前PDF文件路径
                    current_pdf_path = gr.State(value=None)

                    # 分册章节编号设置
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📚</span>分册章节编号设置
                            </h3>
                        """)

                        with gr.Row():
                            with gr.Column(scale=1):
                                volume_code = gr.Textbox(
                                    label="📖 分册编号",
                                    value="04",
                                    placeholder="如：04",
                                    info="🏗️ 设置分册编号（如：04市政工程）"
                                )
                            with gr.Column(scale=2):
                                chapter_codes = gr.Textbox(
                                    label="📑 章节编号",
                                    value="01,02,03",
                                    placeholder="如：01,02,03",
                                    info="📋 设置章节编号，用逗号分隔（如：01通用,02道路,03桥涵）"
                                )

                        gr.HTML("""
                            <div style="margin-top: 10px; padding: 12px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px; border-left: 4px solid #667eea;">
                                <p style="margin: 0; color: #667eea; font-size: 0.9em;">
                                    <span style="font-size: 1.1em;">💡</span> <strong>编号规则</strong>：识别后的定额编号将自动添加前缀，格式为：分册-章节-原编号<br>
                                    <span style="font-size: 1.1em;">📝</span> <strong>示例</strong>：原编号"1-1"将变为"04-01-1-1"、"04-02-1-1"、"04-03-1-1"
                                </p>
                            </div>
                        """)

                    # 页码设置和处理卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📄</span>自定义页码范围设置
                            </h3>
                        """)

                        # 页码范围设置
                        with gr.Row():
                            with gr.Column(scale=1):
                                start_page = gr.Number(
                                    label="📍 起始页码",
                                    value=1,
                                    minimum=1,
                                    precision=0
                                )
                            with gr.Column(scale=1):
                                end_page = gr.Number(
                                    label="📍 结束页码",
                                    value=5,
                                    minimum=1,
                                    precision=0
                                )

                        # 页码设置提示
                        gr.HTML("""
                            <div style="margin-top: 10px; padding: 12px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px; border-left: 4px solid #667eea;">
                                <p style="margin: 0; color: #667eea; font-size: 0.9em;">
                                    <span style="font-size: 1.1em;">💡</span> <strong>整体识别过程太长，建议按章节页面提取，后续将所有数据合并处理</strong>
                                </p>
                            </div>
                        """)

                        # 处理按钮
                        gr.HTML("<div style='margin: 20px 0;'></div>")
                        process_btn = gr.Button(
                            "🚀 开始智能提取",
                            variant="primary",
                            size="lg",
                            elem_classes="btn-primary pulse"
                        )

                with gr.Column(scale=1):
                    # 状态监控卡片
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📊</span>实时状态监控
                            </h3>
                        """)

                        # 处理状态
                        status_text = gr.Textbox(
                            label="🔄 处理状态",
                            lines=8,
                            interactive=False,
                            placeholder="等待开始处理..."
                        )

                        # 模型状态显示
                        model_status = gr.Textbox(
                            label="🤖 模型状态",
                            lines=4,
                            interactive=False,
                            value="💡 点击刷新按钮检查模型状态"
                        )

            # 结果输出区域
            with gr.Row():
                with gr.Column():
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📥</span>结果下载
                            </h3>
                        """)
                        csv_output = gr.File(
                            label="💾 下载CSV文件",
                            visible=False
                        )

            # 处理详情展示
            with gr.Row():
                with gr.Column():
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📈</span>处理详情分析
                            </h3>
                        """)
                        processing_details = gr.Dataframe(
                            label="📊 每页处理详情",
                            headers=["页码", "状态", "模型", "耗时(秒)", "提取项数", "识别结果长度"],
                            datatype=["number", "str", "str", "number", "number", "number"],
                            visible=False
                        )

                        # 提取结果预览 - 动态列头
                        extraction_preview = gr.Dataframe(
                            label="🔍 提取结果预览",
                            visible=False,
                            wrap=True,  # 允许文本换行
                            interactive=False  # 只读模式
                        )

            # 档案管理区域
            gr.HTML("<hr class='divider'>")

            with gr.Group(elem_classes="archive-section fade-in-up"):
                gr.HTML("""
                    <h2 style="color: #667eea; text-align: center; margin-bottom: 30px;">
                        <span class="icon">📁</span>智能档案管理系统
                    </h2>
                    <p style="text-align: center; color: #666; margin-bottom: 30px;">
                        完整的文件预览、批量管理、智能合并功能
                    </p>
                """)

                with gr.Row():
                    with gr.Column(scale=2):
                        # 文件管理操作卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🛠️</span>文件管理操作
                                </h3>
                            """)

                            # 文件列表刷新按钮
                            with gr.Row():
                                refresh_files_btn = gr.Button("🔄 刷新文件列表", elem_classes="btn-secondary")
                                delete_selected_btn = gr.Button("🗑️ 删除选中文件", elem_classes="btn-secondary")
                                merge_selected_btn = gr.Button("🔗 合并选中文件", elem_classes="btn-primary")

                            # 文件列表
                            files_list = gr.CheckboxGroup(
                                label="📋 输出文件列表",
                                choices=[],
                                value=[],
                                interactive=True,
                                info="💡 选择文件进行预览、删除或合并操作"
                            )

                    with gr.Column(scale=1):
                        # 文件预览卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">👀</span>文件预览
                                </h3>
                            """)
                            file_preview = gr.HTML(
                                label="📄 文件内容预览",
                                value="""
                                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                        <div style="font-size: 2.5em; margin-bottom: 15px;">👀</div>
                                        <h4 style="color: #667eea; margin-bottom: 10px;">智能文件预览</h4>
                                        <p style="color: #666;">选择文件查看完整内容预览</p>
                                        <p style="color: #999; font-size: 0.9em;">支持CSV文件完整内容显示</p>
                                    </div>
                                """
                            )

                        # 合并配置卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🔗</span>智能文件合并
                                </h3>
                            """)
                            merge_filename = gr.Textbox(
                                label="📝 合并后文件名",
                                placeholder="merged_quota_data.csv",
                                value="merged_quota_data.csv",
                                info="💡 自动添加.csv扩展名"
                            )
                            merge_mode = gr.Radio(
                                label="🎯 合并模式",
                                choices=[
                                    ("📄 按页码排序合并", "by_page"),
                                    ("📝 按文件名排序合并", "by_filename"),
                                    ("⏰ 按创建时间排序合并", "by_time")
                                ],
                                value="by_page",
                                info="💡 推荐使用页码排序保持逻辑顺序"
                            )



            # MCP数据库转换工具
            gr.HTML("<hr class='divider'>")

            with gr.Group(elem_classes="database-section fade-in-up"):
                gr.HTML("""
                    <h2 style="color: #667eea; text-align: center; margin-bottom: 30px;">
                        <span class="icon">🗄️</span>MCP数据库转换工具
                    </h2>
                    <p style="text-align: center; color: #666; margin-bottom: 30px;">
                        将CSV文件转换为多种数据库格式，支持SQLite、MySQL、PostgreSQL、MongoDB等
                    </p>
                """)

                with gr.Row():
                    with gr.Column(scale=2):
                        # 数据库转换配置卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🔧</span>转换配置
                                </h3>
                            """)

                            db_format = gr.Radio(
                                label="🗄️ 数据库格式",
                                choices=[
                                    ("📱 SQLite数据库文件", "sqlite"),
                                    ("🐬 MySQL SQL脚本", "mysql"),
                                    ("🐘 PostgreSQL SQL脚本", "postgresql"),
                                    ("🏢 SQL Server SQL脚本", "sql_server"),
                                    ("🏛️ Oracle SQL脚本", "oracle"),
                                    ("🍃 MongoDB JSON导出", "mongodb")
                                ],
                                value="sqlite",
                                info="💡 SQLite适合本地使用，SQL脚本适合服务器部署，MongoDB JSON适合NoSQL数据库"
                            )

                            db_filename = gr.Textbox(
                                label="📝 输出文件名",
                                placeholder="quota_database.db",
                                value="quota_database.db",
                                info="💡 系统会自动添加正确的文件扩展名"
                            )

                            with gr.Row():
                                convert_selected_btn = gr.Button("🔄 转换选中文件", elem_classes="btn-primary")
                                preview_db_btn = gr.Button("👀 预览数据库", elem_classes="btn-secondary")

                    with gr.Column(scale=1):
                        # 转换状态卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">📊</span>转换状态
                                </h3>
                            """)
                            db_conversion_status = gr.Textbox(
                                label="🔄 转换状态",
                                interactive=False,
                                visible=False,
                                placeholder="等待开始转换..."
                            )

                        # 数据库预览卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🔍</span>数据库预览
                                </h3>
                            """)
                            db_preview = gr.HTML(
                                label="🗄️ 数据库内容预览",
                                value="""
                                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                        <div style="font-size: 2.5em; margin-bottom: 15px;">🗄️</div>
                                        <h4 style="color: #667eea; margin-bottom: 10px;">数据库预览</h4>
                                        <p style="color: #666;">转换数据库后查看完整预览</p>
                                        <p style="color: #999; font-size: 0.9em;">支持表结构和数据内容预览</p>
                                    </div>
                                """
                            )

            # 输出文件下载区域
            with gr.Row():
                with gr.Column():
                    # 合并文件输出
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">📥</span>合并文件下载
                            </h3>
                        """)
                        merged_file_output = gr.File(
                            label="💾 下载合并后的文件",
                            visible=False
                        )

                with gr.Column():
                    # 数据库文件输出
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h3 style="color: #667eea; margin-bottom: 15px;">
                                <span class="icon">🗄️</span>数据库文件下载
                            </h3>
                        """)
                        db_file_output = gr.File(
                            label="💾 下载数据库文件",
                            visible=False
                        )

            # 定额创建工具
            gr.HTML("<hr class='divider'>")

            with gr.Group(elem_classes="database-section fade-in-up"):
                gr.HTML("""
                    <h2 style="color: #667eea; text-align: center; margin-bottom: 30px;">
                        <span class="icon">🔧</span>定额创建工具
                    </h2>
                    <p style="text-align: center; color: #666; margin-bottom: 30px;">
                        创建定额数据库，实现定额项与资源的关联管理，支持智能查询和价格计算
                    </p>
                """)

                with gr.Row():
                    with gr.Column(scale=2):
                        # 数据库创建配置卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">🔨</span>创建定额数据库
                                </h3>
                            """)

                            enterprise_db_type = gr.Radio(
                                label="🗄️ 数据库类型",
                                choices=[
                                    ("📱 SQLite本地数据库", "sqlite"),
                                    ("🐬 MySQL数据库", "mysql"),
                                    ("🐘 PostgreSQL数据库", "postgresql"),
                                    ("🍃 MongoDB文档数据库", "mongodb")
                                ],
                                value="sqlite",
                                info="💡 推荐使用SQLite进行本地测试，MongoDB适合大数据和灵活查询",
                                interactive=True,
                                elem_id="enterprise_db_type"
                            )

                            with gr.Row():
                                with gr.Column(scale=3):
                                    enterprise_db_name = gr.Textbox(
                                        label="📂 数据库名称",
                                        placeholder="enterprise_quota.db",
                                        value="enterprise_quota.db",
                                        info="💾 SQLite文件路径或数据库名称"
                                    )
                                with gr.Column(scale=1):
                                    merge_strategy = gr.Dropdown(
                                        label="📝 数据合并策略",
                                        choices=[
                                            ("智能合并（推荐）", "smart_merge"),
                                            ("完全覆盖", "replace"),
                                            ("添加时间戳", "timestamp")
                                        ],
                                        value="smart_merge",
                                        info="选择如何处理现有数据"
                                    )

                            with gr.Row():
                                enterprise_db_host = gr.Textbox(
                                    label="🌐 数据库地址",
                                    placeholder="localhost",
                                    value="localhost",
                                    visible=False,
                                    info="🔗 MySQL/PostgreSQL服务器地址"
                                )
                                enterprise_db_port = gr.Number(
                                    label="🔌 端口",
                                    value=3306,
                                    visible=False,
                                    info="📡 数据库端口号"
                                )

                            with gr.Row():
                                enterprise_default_db = gr.Textbox(
                                    label="🗄️ 默认数据库",
                                    placeholder="postgres",
                                    value="postgres",
                                    visible=False,
                                    info="🔗 PostgreSQL默认连接数据库（通常为postgres）"
                                )

                            with gr.Row():
                                enterprise_db_user = gr.Textbox(
                                    label="👤 用户名",
                                    placeholder="root",
                                    visible=False,
                                    info="🔐 数据库用户名"
                                )
                                enterprise_db_password = gr.Textbox(
                                    label="🔒 密码",
                                    type="password",
                                    visible=False,
                                    info="🛡️ 数据库密码"
                                )

                            with gr.Row():
                                test_db_connection_btn = gr.Button("🔍 测试连接", elem_classes="btn-secondary", visible=False)
                                create_enterprise_db_btn = gr.Button("🚀 创建定额数据库", elem_classes="btn-primary")

                            enterprise_db_test_result = gr.Textbox(
                                label="🔗 连接测试结果",
                                interactive=False,
                                lines=3,
                                visible=False,
                                placeholder="点击'测试连接'按钮检查数据库连接状态..."
                            )

                        # 文件选择卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">📋</span>智能数据文件选择
                                </h3>
                            """)

                            gr.HTML("""
                                <div style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
                                     padding: 15px; border-radius: 10px; margin-bottom: 15px; border: 1px solid rgba(102, 126, 234, 0.2);">
                                    <h4 style="color: #667eea; margin-bottom: 10px;">🧠 智能处理模式</h4>
                                    <p style="margin: 0; color: #666;">
                                        • 支持跨区选择：可同时选择定额项和资源文件<br>
                                        • 智能合并：自动合并同类型文件<br>
                                        • 关联计算：自动建立定额项与资源的关联关系<br>
                                        • 价格计算：根据资源消耗自动计算定额项单价
                                    </p>
                                </div>
                            """)

                            parent_quotas_files = gr.CheckboxGroup(
                                label="📊 定额项文件 (parent_quotas*.csv)",
                                choices=[],
                                value=[],
                                info="🎯 选择包含定额项数据的CSV文件（可多选）"
                            )

                            child_resources_files = gr.CheckboxGroup(
                                label="🔧 资源文件 (child_resources*.csv)",
                                choices=[],
                                value=[],
                                info="⚙️ 选择包含资源数据的CSV文件（可多选）"
                            )

                            refresh_enterprise_files_btn = gr.Button("🔄 刷新文件列表", elem_classes="btn-secondary")

                    with gr.Column(scale=1):
                        # 创建状态卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">📈</span>创建状态
                                </h3>
                            """)
                            enterprise_creation_status = gr.Textbox(
                                label="📋 创建状态",
                                interactive=False,
                                lines=8,
                                placeholder="等待创建定额数据库..."
                            )

                        # 数据库统计卡片
                        with gr.Group(elem_classes="feature-card"):
                            gr.HTML("""
                                <h3 style="color: #667eea; margin-bottom: 15px;">
                                    <span class="icon">📊</span>数据库统计
                                </h3>
                            """)
                            enterprise_db_stats = gr.HTML(
                                value="""
                                    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                        <div style="font-size: 2.5em; margin-bottom: 15px;">📊</div>
                                        <h4 style="color: #667eea; margin-bottom: 10px;">数据库统计</h4>
                                        <p style="color: #666;">创建数据库后查看统计信息</p>
                                    </div>
                                """
                            )

            # 定额查询管理界面


            # 高级定额管理系统
            if self.advanced_quota_interface:
                with gr.Group(elem_classes="archive-section fade-in-up"):
                    gr.HTML("""
                        <h2 style="color: #667eea; text-align: center; margin-bottom: 30px;">
                            <span class="icon">⚡</span>高级定额管理系统
                        </h2>
                        <p style="text-align: center; color: #666; margin-bottom: 30px;">
                            多数据库支持、智能数据浏览、关联分析、资源价格管理的完整解决方案
                        </p>
                    """)

                    # 创建高级管理界面的标签页
                    with gr.Tabs():
                        with gr.Tab("🔗 数据库连接"):
                            advanced_connection_components = self.advanced_quota_interface.create_database_connection_interface()

                        with gr.Tab("🗂️ 数据库浏览"):
                            advanced_browser_components = self.advanced_quota_interface.create_database_browser_interface()

                        with gr.Tab("🔍 智能搜索"):
                            advanced_search_components = self.advanced_quota_interface.create_quota_search_interface()

                        with gr.Tab("💰 资源价格"):
                            advanced_price_components = self.advanced_quota_interface.create_resource_price_interface()

                        with gr.Tab("📊 信息价识别"):
                            # 使用独立的信息价识别界面
                            try:
                                from src.intelligent_price_info_interface import IntelligentPriceInfoInterface
                                price_info_interface = IntelligentPriceInfoInterface()
                                advanced_price_info_components = price_info_interface.create_price_info_recognition_interface()

                                # 为信息价界面添加加载事件
                                def init_price_interface():
                                    return price_info_interface.get_stored_price_pdfs()

                                # 在界面加载时初始化已存储PDF列表
                                interface.load(
                                    fn=init_price_interface,
                                    outputs=[advanced_price_info_components.get('stored_price_pdf_dropdown')]
                                )

                            except ImportError as e:
                                print(f"警告: 无法导入智能信息价识别界面: {e}")
                                gr.HTML("""
                                    <div style="text-align: center; padding: 20px; color: #666;">
                                        <h3>⚠️ 信息价识别功能不可用</h3>
                                        <p>智能信息价识别模块未正确加载</p>
                                    </div>
                                """)
                                advanced_price_info_components = {}

                        with gr.Tab("📁 PDF管理"):
                            # PDF文件管理界面
                            pdf_management_components = self.pdf_management.create_pdf_management_interface()

            # 隐藏状态字段
            archive_status = gr.Textbox(
                label="档案管理状态",
                interactive=False,
                visible=False
            )

            # 页面底部信息
            gr.HTML("""
                <div style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 20px; border: 1px solid rgba(102, 126, 234, 0.2);">
                    <h3 style="color: #667eea; margin-bottom: 20px;">🎉 系统功能完整</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; text-align: left; margin-bottom: 20px;">
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">🤖 智能识别</h4>
                            <p style="color: #666; font-size: 0.9em;">24个AI模型可选<br>专业OCR表格识别<br>高精度数据提取</p>
                        </div>
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">💰 价格计算</h4>
                            <p style="color: #666; font-size: 0.9em;">自动单价匹配<br>智能合价计算<br>百分比项处理</p>
                        </div>
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">📁 档案管理</h4>
                            <p style="color: #666; font-size: 0.9em;">完整文件预览<br>批量管理操作<br>智能文件合并</p>
                        </div>
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">🗄️ 数据库转换</h4>
                            <p style="color: #666; font-size: 0.9em;">6种数据库格式<br>智能结构分析<br>完整预览功能</p>
                        </div>
                        <div style="padding: 15px; background: white; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                            <h4 style="color: #667eea; margin-bottom: 10px;">🔧 定额创建工具</h4>
                            <p style="color: #666; font-size: 0.9em;">定额数据库创建<br>智能查询管理<br>资源关联分析</p>
                        </div>
                    </div>
                    <p style="color: #999; font-size: 0.9em; margin-bottom: 0;">
                        <span class="icon">🏗️</span>北京市2021消耗定额智能提取工具 |
                        <span class="icon">🚀</span>专业级AI识别 |
                        <span class="icon">💎</span>企业级功能
                    </p>
                </div>

                <!-- 版权信息 -->
                <div style="text-align: center; margin-top: 30px; padding: 20px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05)); border-radius: 15px; border: 1px solid rgba(102, 126, 234, 0.1);">
                    <p style="color: #888; font-size: 0.85em; margin: 0;">
                        © 2024 Always派智能研究工作室版权所有
                    </p>
                    <p style="color: #999; font-size: 0.8em; margin: 5px 0 0 0;">
                        联系方式：<a href="mailto:<EMAIL>" style="color: #667eea; text-decoration: none;"><EMAIL></a>
                    </p>
                </div>
            """)

            # 简化的模型状态显示（隐藏复杂配置）
            config_modal = gr.HTML(visible=False)  # 占位符，保持兼容性


            # 已存储PDF管理函数
            def get_stored_quota_pdfs():
                """获取已存储的定额PDF列表"""
                try:
                    stored_pdfs = self.pdf_storage.get_stored_pdfs("quota")
                    choices = []
                    for pdf_info in stored_pdfs:
                        display_name = f"{pdf_info.get('original_name', 'Unknown')} ({pdf_info.get('size_mb', 0)} MB)"
                        choices.append((display_name, pdf_info.get('id', '')))
                    return gr.update(choices=choices, value=None)
                except Exception as e:
                    self.logger.error(f"获取已存储PDF列表失败: {e}")
                    return gr.update(choices=[], value=None)

            def show_stored_pdf_info(pdf_id):
                """显示已存储PDF的详细信息"""
                try:
                    if not pdf_id:
                        return "<p style='color: #666; text-align: center;'>选择PDF文件查看详情</p>"

                    pdf_info = self.pdf_storage.get_pdf_info(pdf_id)
                    if not pdf_info:
                        return "<p style='color: #f56565; text-align: center;'>文件不存在</p>"

                    # 格式化上传时间
                    upload_time = pdf_info.get("upload_time", "")
                    if upload_time:
                        try:
                            from datetime import datetime
                            dt = datetime.fromisoformat(upload_time.replace("Z", "+00:00"))
                            upload_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            pass

                    html = f"""
                    <div style="background: #f8f9fa; border-radius: 10px; padding: 15px; margin: 10px 0;">
                        <h4 style="color: #667eea; margin-top: 0; font-size: 1.1em;">📄 {pdf_info.get('original_name', 'Unknown')}</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                            <div><strong>文件大小:</strong> {pdf_info.get('size_mb', 0)} MB</div>
                            <div><strong>上传时间:</strong> {upload_time}</div>
                        </div>
                        <div style="margin-top: 10px; font-size: 0.8em; color: #666;">
                            <strong>存储路径:</strong><br>
                            <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px; word-break: break-all;">
                                {pdf_info.get('stored_path', 'N/A')}
                            </code>
                        </div>
                    </div>
                    """
                    return html

                except Exception as e:
                    return f"<p style='color: #f56565; text-align: center;'>获取文件信息失败: {str(e)}</p>"

            def load_stored_pdf(pdf_id):
                """加载已存储的PDF文件"""
                try:
                    if not pdf_id:
                        return (
                            "<p style='text-align: center; color: #666;'>📄 选择PDF文件后将显示浏览器</p>",
                            gr.update(visible=False),
                            gr.update(value=1),
                            gr.update(value="0"),
                            None  # pdf_input
                        )

                    pdf_info = self.pdf_storage.get_pdf_info(pdf_id)
                    if not pdf_info:
                        error_html = """
                        <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                            <h3 style="margin-top: 0; color: #c62828;">❌ 文件不存在</h3>
                            <p style="color: #666;">选择的PDF文件不存在或已被删除</p>
                        </div>
                        """
                        return (
                            error_html,
                            gr.update(visible=False),
                            gr.update(value=1),
                            gr.update(value="0"),
                            None
                        )

                    pdf_path = pdf_info.get('stored_path', '')
                    if not os.path.exists(pdf_path):
                        error_html = """
                        <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                            <h3 style="margin-top: 0; color: #c62828;">❌ 文件路径无效</h3>
                            <p style="color: #666;">PDF文件路径不存在，可能已被移动或删除</p>
                        </div>
                        """
                        return (
                            error_html,
                            gr.update(visible=False),
                            gr.update(value=1),
                            gr.update(value="0"),
                            None
                        )

                    # 使用现有的load_pdf逻辑
                    import fitz  # PyMuPDF

                    # 打开PDF文件获取总页数
                    doc = fitz.open(pdf_path)
                    total_pages = len(doc)
                    doc.close()

                    # 显示第一页
                    page_html = render_pdf_page(pdf_path, 1)

                    self.logger.info(f"📄 已加载存储的定额PDF: {pdf_info.get('original_name', 'Unknown')}")

                    return (
                        page_html,
                        gr.update(visible=True),
                        gr.update(value=1, maximum=total_pages),
                        gr.update(value=str(total_pages)),
                        pdf_path  # 返回文件路径供后续处理使用
                    )

                except Exception as e:
                    error_html = f"""
                    <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                        <h3 style="margin-top: 0; color: #c62828;">❌ PDF加载失败</h3>
                        <p style="color: #666;">错误信息: {str(e)}</p>
                        <p style="color: #666;">请尝试重新选择文件</p>
                    </div>
                    """
                    return (
                        error_html,
                        gr.update(visible=False),
                        gr.update(value=1),
                        gr.update(value="0"),
                        None
                    )

            # PDF浏览器函数
            def load_pdf(pdf_file):
                """加载PDF文件并初始化浏览器"""
                if not pdf_file:
                    return (
                        "<p style='text-align: center; color: #666;'>📄 上传PDF文件后将显示浏览器</p>",
                        gr.update(visible=False),
                        gr.update(value=1),
                        gr.update(value="0"),
                        None  # current_pdf_path
                    )

                try:
                    import fitz  # PyMuPDF

                    # 存储PDF文件到系统
                    original_name = os.path.basename(pdf_file) if pdf_file else "unknown.pdf"
                    success, stored_path, file_info = self.pdf_storage.store_quota_pdf(pdf_file, original_name)

                    if success:
                        if "existing_id" in file_info:
                            self.logger.info(f"📄 定额PDF文件已存在: {original_name} (ID: {file_info['existing_id']})")
                        else:
                            self.logger.info(f"📄 定额PDF文件已保存: {original_name} -> {file_info['id']}")
                            self.logger.info(f"   存储路径: {stored_path}")
                            self.logger.info(f"   文件大小: {file_info['size_mb']} MB")
                    else:
                        self.logger.warning(f"⚠️ 定额PDF文件存储失败: {file_info.get('error', '未知错误')}")

                    # 打开PDF文件获取总页数
                    doc = fitz.open(pdf_file)
                    total_pages = len(doc)
                    doc.close()

                    # 显示第一页
                    page_html = render_pdf_page(pdf_file, 1)

                    return (
                        page_html,
                        gr.update(visible=True),
                        gr.update(value=1, maximum=total_pages),
                        gr.update(value=str(total_pages)),
                        pdf_file  # 更新current_pdf_path状态
                    )

                except Exception as e:
                    error_html = f"""
                    <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                        <h3 style="margin-top: 0; color: #c62828;">❌ PDF加载失败</h3>
                        <p style="color: #666;">错误信息: {str(e)}</p>
                        <p style="color: #666;">请确保上传的是有效的PDF文件</p>
                    </div>
                    """
                    return (
                        error_html,
                        gr.update(visible=False),
                        gr.update(value=1),
                        gr.update(value="0"),
                        None  # 清空current_pdf_path状态
                    )

            def render_pdf_page(pdf_file, page_num, zoom_level=2.0):
                """渲染指定页面"""
                try:
                    import fitz  # PyMuPDF
                    import base64

                    # 打开PDF文件
                    doc = fitz.open(pdf_file)
                    total_pages = len(doc)

                    # 确保页码在有效范围内
                    page_num = max(1, min(page_num, total_pages))
                    page = doc[page_num - 1]  # fitz使用0基索引

                    # 渲染页面为图片
                    mat = fitz.Matrix(zoom_level, zoom_level)  # 缩放因子
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.tobytes("png")

                    # 转换为base64
                    img_base64 = base64.b64encode(img_data).decode()

                    # 生成HTML
                    page_html = f"""
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9; text-align: center;">
                        <h3 style="margin-top: 0; color: #333;">📄 第 {page_num} 页 / 共 {total_pages} 页</h3>
                        <div style="margin: 16px 0; overflow: auto; max-height: 600px;">
                            <img src="data:image/png;base64,{img_base64}"
                                 style="max-width: 100%; border: 1px solid #ccc; border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />
                        </div>
                        <p style="margin-bottom: 0; color: #666; font-size: 12px;">
                            💡 使用上方的导航按钮浏览其他页面，或直接输入页码跳转
                        </p>
                    </div>
                    """

                    doc.close()
                    return page_html

                except Exception as e:
                    return f"""
                    <div style="border: 1px solid #ffcdd2; border-radius: 8px; padding: 16px; background: #ffebee;">
                        <h3 style="margin-top: 0; color: #c62828;">❌ 页面渲染失败</h3>
                        <p style="color: #666;">错误信息: {str(e)}</p>
                    </div>
                    """

            def show_page(current_pdf_path, page_num):
                """显示指定页面"""
                if not current_pdf_path:
                    return "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

                return render_pdf_page(current_pdf_path, int(page_num))

            def prev_page(current_pdf_path, current_page):
                """上一页"""
                if not current_pdf_path:
                    return current_page, "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

                new_page = max(1, current_page - 1)
                page_html = render_pdf_page(current_pdf_path, new_page)
                return new_page, page_html

            def next_page(current_pdf_path, current_page, total_pages):
                """下一页"""
                if not current_pdf_path:
                    return current_page, "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

                max_pages = int(total_pages) if total_pages.isdigit() else 1
                new_page = min(max_pages, current_page + 1)
                page_html = render_pdf_page(current_pdf_path, new_page)
                return new_page, page_html

            def zoom_page(current_pdf_path, current_page):
                """放大查看当前页面"""
                if not current_pdf_path:
                    return "<p style='text-align: center; color: #666;'>📄 请先选择PDF文件</p>"

                # 使用更高的缩放级别
                return render_pdf_page(current_pdf_path, current_page, zoom_level=3.0)

            # 简化的API密钥保存功能
            def save_api_key(api_key):
                """保存API密钥"""
                if not api_key:
                    return "⚠️ 请输入API密钥"

                try:
                    # 保存到环境变量文件
                    success = self._save_api_key("DASHSCOPE_API_KEY", api_key)
                    if success:
                        # 重新加载AI处理器
                        self.ai_processor.reload_api_keys()
                        return f"✅ API密钥已保存 (***{api_key[-4:]})"
                    else:
                        return "❌ 保存失败"
                except Exception as e:
                    return f"❌ 保存失败: {str(e)}"

            def test_quota_model_connection(model_type):
                """测试定额识别模型连接"""
                try:
                    # 显示测试中状态
                    testing_html = """
                    <div style="padding: 10px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <p style="margin: 0; color: #856404;">
                            🔄 正在测试模型连接，请稍候...
                        </p>
                    </div>
                    """

                    # 异步测试连接
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        success, message = loop.run_until_complete(
                            self.ai_processor.test_model_connection(model_type)
                        )
                    finally:
                        loop.close()

                    # 生成结果HTML
                    if success:
                        result_html = f"""
                        <div style="padding: 10px; background: #d4edda; border-radius: 8px; border-left: 4px solid #28a745;">
                            <p style="margin: 0; color: #155724;">
                                {message}
                            </p>
                        </div>
                        """
                    else:
                        result_html = f"""
                        <div style="padding: 10px; background: #f8d7da; border-radius: 8px; border-left: 4px solid #dc3545;">
                            <p style="margin: 0; color: #721c24;">
                                {message}
                            </p>
                        </div>
                        """

                    return result_html

                except Exception as e:
                    error_html = f"""
                    <div style="padding: 10px; background: #f8d7da; border-radius: 8px; border-left: 4px solid #dc3545;">
                        <p style="margin: 0; color: #721c24;">
                            ❌ 测试异常: {str(e)}
                        </p>
                    </div>
                    """
                    return error_html

            # 处理函数
            def process_wrapper(current_pdf_path, model_type, start_page, end_page, volume_code, chapter_codes, progress=gr.Progress()):
                if not current_pdf_path:
                    return None, "请先选择PDF文件", None, None, gr.update(visible=False)

                if not model_type or model_type == "none":
                    return None, "请选择AI模型", None, None, gr.update(visible=False)

                if start_page > end_page:
                    return None, "起始页码不能大于结束页码", None, None, gr.update(visible=False)

                # 解析模型类型 - 从下拉菜单的值中提取模型键值
                print(f"调试：接收到的model_type: {model_type}")

                # 如果model_type是显示名称，需要转换为模型键值
                actual_model_type = model_type
                if isinstance(model_type, str):
                    # 获取当前可用模型列表
                    available_models = self.ai_processor.get_available_models()

                    # 如果传入的是显示名称，查找对应的键值
                    for key, display_name in available_models.items():
                        if display_name == model_type:
                            actual_model_type = key
                            break

                print(f"调试：实际使用的model_type: {actual_model_type}")

                # 运行异步处理
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    csv_path, log_message, details = loop.run_until_complete(
                        self.process_pdf(current_pdf_path, int(start_page), int(end_page), actual_model_type, volume_code, chapter_codes, progress)
                    )

                    # 格式化处理详情
                    details_update = gr.update(visible=False)
                    preview_update = gr.update(visible=False)

                    if details:
                        details_data = [
                            [
                                d["page"],
                                d["status"],
                                self.ai_processor.supported_models.get(d["model"], d["model"]),
                                round(d.get("duration", 0), 2),
                                d.get("extracted_items", 0),
                                d.get("result_length", 0)
                            ]
                            for d in details
                        ]
                        details_update = gr.update(value=details_data, visible=True)

                        # 生成预览数据 - 从CSV文件重新读取完整数据
                        preview_update = gr.update(visible=False)  # 默认不显示
                        if csv_path:
                            try:
                                import pandas as pd
                                df = pd.read_csv(csv_path, encoding='utf-8-sig')

                                if not df.empty:
                                    # 显示所有数据，最多200行以确保完整显示
                                    max_preview_rows = min(len(df), 200)
                                    preview_data = df.head(max_preview_rows).values.tolist()

                                    # 创建包含列头的数据
                                    column_headers = df.columns.tolist()
                                    # 将列头作为第一行添加到数据中
                                    preview_data_with_headers = [column_headers] + preview_data

                                    preview_update = gr.update(
                                        value=preview_data_with_headers,
                                        visible=True
                                    )
                                    print(f"✅ 预览数据生成成功：显示 {max_preview_rows}/{len(df)} 行，{len(column_headers)} 列")
                                    print(f"📊 列头：{column_headers}")
                                else:
                                    print("⚠️ CSV文件为空")

                            except Exception as e:
                                print(f"❌ 生成预览失败: {e}")
                                preview_update = gr.update(visible=False)

                    return csv_path, log_message, details_update, preview_update, gr.update(visible=bool(csv_path))
                finally:
                    loop.close()

            # 档案管理函数
            def refresh_files_list():
                """刷新输出文件列表"""
                try:
                    import os
                    import glob
                    from datetime import datetime

                    output_dir = "output"
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)
                        return gr.update(choices=[]), "📁 输出目录为空"

                    # 获取所有CSV文件
                    csv_files = glob.glob(os.path.join(output_dir, "*.csv"))

                    if not csv_files:
                        return gr.update(choices=[]), "📁 没有找到CSV文件"

                    # 获取文件信息
                    file_info = []
                    for file_path in csv_files:
                        filename = os.path.basename(file_path)
                        file_size = os.path.getsize(file_path)
                        mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                        # 格式化显示
                        size_mb = file_size / 1024 / 1024
                        time_str = mod_time.strftime("%Y-%m-%d %H:%M")
                        display_name = f"{filename} ({size_mb:.2f}MB, {time_str})"

                        file_info.append((display_name, filename))

                    # 按创建时间排序（最新的在前）
                    file_info.sort(key=lambda x: min(os.path.getctime(os.path.join(output_dir, x[1])),
                                                    os.path.getmtime(os.path.join(output_dir, x[1]))), reverse=True)

                    choices = [display_name for display_name, _ in file_info]
                    status_msg = f"📁 找到 {len(choices)} 个CSV文件"

                    return gr.update(choices=choices, value=[]), status_msg

                except Exception as e:
                    return gr.update(choices=[]), f"❌ 刷新文件列表失败: {str(e)}"

            def preview_selected_file(selected_files):
                """预览选中的文件"""
                if not selected_files:
                    return "<p style='text-align: center; color: #666;'>选择文件查看预览</p>"

                try:
                    import pandas as pd
                    import os

                    # 获取第一个选中文件的实际文件名
                    selected_display = selected_files[0]
                    filename = selected_display.split(" (")[0]  # 提取文件名
                    file_path = os.path.join("output", filename)

                    if not os.path.exists(file_path):
                        return f"<p style='color: red;'>❌ 文件不存在: {filename}</p>"

                    # 读取CSV文件
                    df = pd.read_csv(file_path, encoding='utf-8-sig')

                    # 生成完整预览HTML
                    preview_html = f"""
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                        <h3 style="margin-top: 0; color: #333;">📄 {filename}</h3>
                        <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                            <p style="margin: 4px 0;"><strong>📊 文件统计:</strong></p>
                            <p style="margin: 4px 0;">• 总行数: {len(df)} 行</p>
                            <p style="margin: 4px 0;">• 列数: {len(df.columns)} 列</p>
                            <p style="margin: 4px 0;">• 文件大小: {os.path.getsize(file_path) / 1024:.2f} KB</p>
                            <p style="margin: 4px 0;">• 列名: {', '.join(df.columns.tolist())}</p>
                        </div>

                        <h4 style="color: #333; margin-bottom: 12px;">📋 完整文件内容:</h4>
                        <div style="overflow: auto; max-height: 600px; border: 1px solid #ccc; border-radius: 4px; background: white;">
                            {df.to_html(index=True, classes='preview-table', table_id='full-preview-table', escape=False)}
                        </div>

                        <div style="margin-top: 12px; padding: 8px; background: #f0f8ff; border-radius: 4px; font-size: 12px; color: #666;">
                            💡 提示: 表格支持滚动查看，显示完整的 {len(df)} 行数据
                        </div>

                        <style>
                        #full-preview-table {{
                            font-size: 11px;
                            border-collapse: collapse;
                            width: 100%;
                            margin: 0;
                        }}
                        #full-preview-table th, #full-preview-table td {{
                            border: 1px solid #ddd;
                            padding: 6px 8px;
                            text-align: left;
                            vertical-align: top;
                        }}
                        #full-preview-table th {{
                            background-color: #f8f9fa;
                            font-weight: bold;
                            position: sticky;
                            top: 0;
                            z-index: 10;
                        }}
                        #full-preview-table tbody tr:nth-child(even) {{
                            background-color: #f9f9f9;
                        }}
                        #full-preview-table tbody tr:hover {{
                            background-color: #e8f4fd;
                        }}
                        #full-preview-table td {{
                            max-width: 200px;
                            word-wrap: break-word;
                            white-space: normal;
                        }}
                        </style>
                    </div>
                    """

                    return preview_html

                except Exception as e:
                    return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"

            def delete_selected_files(selected_files):
                """删除选中的文件"""
                if not selected_files:
                    return gr.update(), "⚠️ 请先选择要删除的文件"

                try:
                    import os

                    deleted_count = 0
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)

                        if os.path.exists(file_path):
                            os.remove(file_path)
                            deleted_count += 1

                    # 刷新文件列表
                    updated_choices, status_msg = refresh_files_list()

                    return updated_choices, f"✅ 成功删除 {deleted_count} 个文件"

                except Exception as e:
                    return gr.update(), f"❌ 删除失败: {str(e)}"

            def merge_selected_files(selected_files, merge_filename, merge_mode):
                """合并选中的文件"""
                if not selected_files:
                    return None, "⚠️ 请先选择要合并的文件", gr.update(visible=False)

                if len(selected_files) < 2:
                    return None, "⚠️ 至少需要选择2个文件进行合并", gr.update(visible=False)

                try:
                    from src.mcp_file_merger import MCPFileMerger
                    import os

                    # 获取实际文件路径
                    file_paths = []
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)
                        if os.path.exists(file_path):
                            file_paths.append(file_path)

                    if not file_paths:
                        return None, "❌ 没有找到有效的文件", gr.update(visible=False)

                    # 确保输出文件名以.csv结尾
                    if not merge_filename.endswith('.csv'):
                        merge_filename += '.csv'

                    output_path = os.path.join("output", merge_filename)

                    # 使用MCP工具合并文件
                    merger = MCPFileMerger()
                    success, message, stats = merger.merge_csv_files(file_paths, output_path, merge_mode)

                    if success:
                        # 生成详细状态信息
                        status_detail = f"""✅ 文件合并成功！

📊 合并统计:
- 合并文件数: {stats['total_files']} 个
- 总数据行数: {stats['total_rows']} 行
- 输出文件大小: {stats['output_size_kb']:.2f} KB
- 合并模式: {stats['merge_mode']}

📁 输出文件: {merge_filename}

📋 源文件详情:"""

                        for file_stat in stats['file_stats']:
                            status_detail += f"\n- {file_stat['filename']}: {file_stat['rows']} 行, {file_stat['size_kb']:.2f} KB"

                        return output_path, status_detail, gr.update(visible=True)
                    else:
                        return None, message, gr.update(visible=False)

                except Exception as e:
                    return None, f"❌ 合并失败: {str(e)}", gr.update(visible=False)

            def update_merge_preview(selected_files, merge_mode):
                """更新合并预览"""
                if not selected_files or len(selected_files) < 2:
                    return "<p style='text-align: center; color: #666;'>选择至少2个文件查看合并预览</p>"

                try:
                    from src.mcp_file_merger import MCPFileMerger
                    import os

                    # 获取实际文件路径
                    file_paths = []
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)
                        if os.path.exists(file_path):
                            file_paths.append(file_path)

                    if not file_paths:
                        return "<p style='color: red;'>❌ 没有找到有效的文件</p>"

                    # 生成合并预览
                    merger = MCPFileMerger()
                    preview_html = merger.generate_merge_preview(file_paths, merge_mode)

                    return preview_html

                except Exception as e:
                    return f"<p style='color: red;'>❌ 生成预览失败: {str(e)}</p>"

            def convert_to_database(selected_files, db_format, db_filename):
                """转换CSV文件为数据库"""
                if not selected_files:
                    return None, "⚠️ 请先选择要转换的CSV文件", gr.update(visible=False), "<p style='color: #666;'>请先转换数据库</p>"

                try:
                    from src.mcp_database_converter import MCPDatabaseConverter
                    import os

                    # 获取实际文件路径
                    file_paths = []
                    for selected_display in selected_files:
                        filename = selected_display.split(" (")[0]  # 提取文件名
                        file_path = os.path.join("output", filename)
                        if os.path.exists(file_path):
                            file_paths.append(file_path)

                    if not file_paths:
                        return None, "❌ 没有找到有效的CSV文件", gr.update(visible=False), "<p style='color: red;'>❌ 没有找到有效文件</p>"

                    # 确保输出文件名有正确的扩展名
                    if db_format == "sqlite":
                        if not db_filename.endswith('.db') and not db_filename.endswith('.sqlite'):
                            db_filename += '.db'
                    elif db_format == "mongodb":
                        if not db_filename.endswith('.json'):
                            db_filename += '.json'
                    else:
                        if not db_filename.endswith('.sql'):
                            db_filename += '.sql'

                    output_path = os.path.join("output", db_filename)

                    # 使用MCP数据库转换工具
                    converter = MCPDatabaseConverter()

                    if db_format == "sqlite":
                        success, message, stats = converter.convert_to_sqlite(file_paths, output_path)
                    elif db_format == "mongodb":
                        success, message, stats = converter.convert_to_mongodb(file_paths, output_path)
                    else:
                        success, message, stats = converter.convert_to_sql_script(file_paths, output_path, db_format)

                    if success:
                        # 生成详细状态信息
                        status_detail = f"""✅ 数据库转换成功！

📊 转换统计:
- 源文件数: {stats['total_files']} 个
- 成功转换: {stats['successful_files']} 个
- 总数据行数: {stats['total_rows']} 行
- 输出格式: {db_format.upper()}
- 输出文件: {db_filename}

📋 生成的{'集合' if db_format == 'mongodb' else '表'}:"""

                        # 根据数据库类型显示不同的信息
                        if db_format == "mongodb":
                            for collection_info in stats.get('collections', []):
                                status_detail += f"\n- {collection_info['name']}: {collection_info['documents']} 个文档 (来源: {collection_info['source_file']})"
                        else:
                            for table_info in stats.get('tables', []):
                                status_detail += f"\n- {table_info['name']}: {table_info['rows']} 行 (来源: {table_info['source_file']})"

                        # 生成预览
                        preview_html = converter.preview_database_file(output_path)

                        return output_path, status_detail, gr.update(visible=True), preview_html
                    else:
                        return None, message, gr.update(visible=False), f"<p style='color: red;'>{message}</p>"

                except Exception as e:
                    error_msg = f"❌ 数据库转换失败: {str(e)}"
                    return None, error_msg, gr.update(visible=False), f"<p style='color: red;'>{error_msg}</p>"

            def preview_database_file(selected_files, db_format, db_filename):
                """预览已生成的数据库文件"""
                try:
                    import os

                    # 确保输出文件名有正确的扩展名
                    if db_format == "sqlite":
                        if not db_filename.endswith('.db') and not db_filename.endswith('.sqlite'):
                            db_filename += '.db'
                    elif db_format == "mongodb":
                        if not db_filename.endswith('.json'):
                            db_filename += '.json'
                    else:
                        if not db_filename.endswith('.sql'):
                            db_filename += '.sql'

                    output_path = os.path.join("output", db_filename)

                    if not os.path.exists(output_path):
                        return "<p style='color: orange;'>⚠️ 数据库文件不存在，请先转换CSV文件</p>"

                    from src.mcp_database_converter import MCPDatabaseConverter
                    converter = MCPDatabaseConverter()

                    preview_html = converter.preview_database_file(output_path)
                    return preview_html

                except Exception as e:
                    return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"

            # 定额创建工具函数
            def refresh_enterprise_files():
                """刷新企业定额文件列表"""
                try:
                    import os
                    import glob

                    output_dir = "output"
                    if not os.path.exists(output_dir):
                        return gr.update(choices=[]), gr.update(choices=[])

                    # 获取parent_quotas文件（包括原始文件和处理后的文件）
                    parent_files = []
                    parent_files.extend(glob.glob(os.path.join(output_dir, "parent_quotas*.csv")))
                    parent_files.extend(glob.glob(os.path.join(output_dir, "processed_parent_quotas*.csv")))

                    # 获取child_resources文件（包括原始文件和处理后的文件）
                    child_files = []
                    child_files.extend(glob.glob(os.path.join(output_dir, "child_resources*.csv")))
                    child_files.extend(glob.glob(os.path.join(output_dir, "processed_child_resources*.csv")))

                    # 按创建时间排序（最新的在前）
                    def sort_files_by_creation_time(file_list):
                        from datetime import datetime
                        file_info = []
                        for file_path in file_list:
                            if os.path.exists(file_path):
                                filename = os.path.basename(file_path)
                                # 获取文件创建时间
                                creation_time = os.path.getctime(file_path)
                                modification_time = os.path.getmtime(file_path)
                                # 使用较早的时间作为创建时间
                                actual_creation_time = min(creation_time, modification_time)

                                file_size = os.path.getsize(file_path)
                                create_time_obj = datetime.fromtimestamp(actual_creation_time)

                                # 格式化显示名称
                                size_mb = file_size / 1024 / 1024
                                time_str = create_time_obj.strftime("%Y-%m-%d %H:%M")
                                display_name = f"{filename} ({size_mb:.2f}MB, {time_str})"

                                file_info.append((display_name, actual_creation_time, filename))

                        # 按创建时间排序（最新的在前）
                        file_info.sort(key=lambda x: x[1], reverse=True)
                        return [item[0] for item in file_info]

                    parent_choices = sort_files_by_creation_time(list(set(parent_files)))
                    child_choices = sort_files_by_creation_time(list(set(child_files)))

                    # 调试信息
                    print(f"🔄 刷新文件列表（按创建时间排序）:")
                    print(f"   - 定额项文件: {len(parent_choices)} 个")
                    print(f"   - 资源文件: {len(child_choices)} 个")
                    if parent_choices:
                        print(f"   - 最新定额项文件: {parent_choices[0].split(' (')[0]}")
                    if child_choices:
                        print(f"   - 最新资源文件: {child_choices[0].split(' (')[0]}")

                    return gr.update(choices=parent_choices), gr.update(choices=child_choices)

                except Exception as e:
                    return gr.update(choices=[]), gr.update(choices=[])

            def update_enterprise_db_config(db_type):
                """根据数据库类型更新配置界面"""
                if db_type == "sqlite":
                    return (
                        gr.update(visible=False),  # host
                        gr.update(visible=False),  # port
                        gr.update(visible=False),  # default_db
                        gr.update(visible=False),  # user
                        gr.update(visible=False),  # password
                        gr.update(visible=False),  # test_btn
                        gr.update(visible=False),  # test_result
                        gr.update(info="💾 SQLite数据库文件路径")
                    )
                elif db_type == "mysql":
                    return (
                        gr.update(visible=True),   # host
                        gr.update(visible=True, value=3306),   # port
                        gr.update(visible=False),  # default_db (MySQL不需要)
                        gr.update(visible=True, placeholder="root"),   # user
                        gr.update(visible=True),   # password
                        gr.update(visible=True),   # test_btn
                        gr.update(visible=True),   # test_result
                        gr.update(info="🗄️ MySQL数据库名称")
                    )
                elif db_type == "postgresql":
                    return (
                        gr.update(visible=True),   # host
                        gr.update(visible=True, value=5432),   # port
                        gr.update(visible=True, value="postgres"),  # default_db
                        gr.update(visible=True, placeholder="postgres"),   # user
                        gr.update(visible=True),   # password
                        gr.update(visible=True),   # test_btn
                        gr.update(visible=True),   # test_result
                        gr.update(info="🗄️ PostgreSQL目标数据库名称")
                    )
                elif db_type == "mongodb":
                    return (
                        gr.update(visible=True),   # host
                        gr.update(visible=True, value=27017),   # port
                        gr.update(visible=False),  # default_db (MongoDB不需要)
                        gr.update(visible=True),   # user
                        gr.update(visible=True),   # password
                        gr.update(visible=True),   # test_btn
                        gr.update(visible=True),   # test_result
                        gr.update(info="🍃 MongoDB数据库名称")
                    )

            def test_database_connection(db_type, db_name, db_host, db_port, default_db, db_user, db_password):
                """测试数据库连接"""
                try:
                    if db_type == "sqlite":
                        return "💡 SQLite数据库无需连接测试，创建时会自动验证路径"

                    elif db_type == "postgresql":
                        try:
                            import psycopg2
                            connection = psycopg2.connect(
                                host=db_host or 'localhost',
                                port=int(db_port) if db_port else 5432,
                                user=db_user or 'postgres',
                                password=db_password or '',
                                database=default_db or 'postgres',
                                connect_timeout=5
                            )
                            connection.close()
                            return f"✅ PostgreSQL连接成功！\n服务器: {db_host}:{db_port}\n数据库: {default_db}\n用户: {db_user}"
                        except ImportError:
                            return "❌ 缺少psycopg2模块，请安装: pip install psycopg2-binary"
                        except Exception as e:
                            return f"❌ PostgreSQL连接失败: {str(e)}\n💡 请确认:\n1. PostgreSQL服务已启动\n2. 用户名密码正确\n3. 数据库存在"

                    elif db_type == "mysql":
                        try:
                            import pymysql
                            connection = pymysql.connect(
                                host=db_host or 'localhost',
                                port=int(db_port) if db_port else 3306,
                                user=db_user or 'root',
                                password=db_password or '',
                                charset='utf8mb4',
                                connect_timeout=5
                            )
                            connection.close()
                            return f"✅ MySQL连接成功！\n服务器: {db_host}:{db_port}\n用户: {db_user}"
                        except ImportError:
                            return "❌ 缺少pymysql模块，请安装: pip install pymysql"
                        except Exception as e:
                            return f"❌ MySQL连接失败: {str(e)}"

                    elif db_type == "mongodb":
                        try:
                            from pymongo import MongoClient
                            if db_user and db_password:
                                connection_string = f"mongodb://{db_user}:{db_password}@{db_host}:{db_port}/"
                            else:
                                connection_string = f"mongodb://{db_host}:{db_port}/"

                            client = MongoClient(connection_string, serverSelectionTimeoutMS=5000)
                            client.server_info()
                            client.close()
                            return f"✅ MongoDB连接成功！\n服务器: {db_host}:{db_port}\n认证: {'是' if db_user else '否'}"
                        except ImportError:
                            return "❌ 缺少pymongo模块，请安装: pip install pymongo"
                        except Exception as e:
                            return f"❌ MongoDB连接失败: {str(e)}"

                    else:
                        return f"❌ 不支持的数据库类型: {db_type}"

                except Exception as e:
                    return f"❌ 连接测试失败: {str(e)}"

            def create_enterprise_database(
                db_type, db_name, db_host, db_port, default_db, db_user, db_password,
                parent_files, child_files, merge_strategy
            ):
                """创建企业定额数据库"""
                try:
                    # 根据合并策略确定最终的数据库名称
                    if merge_strategy == "timestamp":
                        final_db_name = self.generate_database_name_with_timestamp(db_name, True)
                    else:
                        final_db_name = db_name

                    # 检查数据库是否存在
                    if db_type == "sqlite":
                        db_path = os.path.join("output", final_db_name)
                        db_exists, db_info = self.check_database_exists(db_type, {'database_path': db_path})
                    elif db_type == "mongodb":
                        json_filename = final_db_name if final_db_name.endswith('.json') else f"{final_db_name}.json"
                        json_path = os.path.join("output", json_filename)
                        db_exists, db_info = self.check_database_exists(db_type, {'database_path': json_path})
                    else:
                        db_exists, db_info = self.check_database_exists(db_type, {'database': final_db_name})

                    # 记录数据库创建信息
                    strategy_desc = {
                        "smart_merge": "智能合并现有数据",
                        "replace": "完全覆盖现有数据",
                        "timestamp": "添加时间戳创建新数据库"
                    }

                    self.logger.info(f"数据库创建策略: {strategy_desc.get(merge_strategy, merge_strategy)}")

                    if db_exists:
                        if merge_strategy == "smart_merge":
                            self.logger.info(f"数据库已存在，将智能合并新数据: {db_info}")
                        elif merge_strategy == "replace":
                            self.logger.warning(f"数据库已存在，将完全覆盖: {db_info}")
                        elif merge_strategy == "timestamp":
                            self.logger.info(f"使用时间戳创建新数据库: {final_db_name}")
                    else:
                        self.logger.info(f"创建新数据库: {final_db_name}")
                    # 尝试多种导入方式
                    try:
                        from src.enterprise_quota_manager import EnterpriseQuotaManager
                    except ImportError:
                        import sys
                        import os
                        sys.path.append('src')
                        from enterprise_quota_manager import EnterpriseQuotaManager

                    import os

                    if not parent_files and not child_files:
                        return "⚠️ 请至少选择一个数据文件", "<p style='color: orange;'>请选择数据文件</p>"

                    # 构建数据库配置（使用带时间戳的名称）
                    if db_type == "sqlite":
                        db_config = {
                            'database_path': os.path.join("output", final_db_name)
                        }
                    elif db_type == "mongodb":
                        # MongoDB使用JSON导出文件
                        json_filename = final_db_name if final_db_name.endswith('.json') else f"{final_db_name}.json"
                        db_config = {
                            'database_path': os.path.join("output", json_filename),
                            'host': db_host,
                            'port': int(db_port),
                            'user': db_user,
                            'password': db_password,
                            'database': final_db_name.replace('.json', '') if final_db_name.endswith('.json') else final_db_name
                        }
                    else:
                        db_config = {
                            'host': db_host,
                            'port': int(db_port),
                            'user': db_user,
                            'password': db_password,
                            'database': final_db_name,
                            'default_db': default_db
                        }

                    # 获取文件路径（提取真实文件名，去除格式化信息）
                    def extract_filename(formatted_name):
                        """从格式化的文件名中提取真实文件名"""
                        if ' (' in formatted_name:
                            # 格式: "filename.csv (size, time)"
                            return formatted_name.split(' (')[0]
                        return formatted_name

                    parent_paths = [os.path.join("output", extract_filename(f)) for f in parent_files if f]
                    child_paths = [os.path.join("output", extract_filename(f)) for f in child_files if f]

                    # 调试信息
                    print(f"🔍 原始文件选择:")
                    for f in parent_files:
                        print(f"   - 定额项: {f}")
                    for f in child_files:
                        print(f"   - 资源: {f}")

                    print(f"📁 提取的文件路径:")
                    for p in parent_paths:
                        print(f"   - 定额项路径: {p}")
                    for p in child_paths:
                        print(f"   - 资源路径: {p}")

                    # 创建管理器并创建数据库
                    manager = EnterpriseQuotaManager()
                    success, message = manager.create_quota_database(
                        db_type, db_config, parent_paths, child_paths, merge_strategy
                    )

                    if success:
                        # 构建成功消息，根据合并策略显示不同信息
                        strategy_messages = {
                            "smart_merge": "🧠 智能合并：新数据已与现有数据智能合并，重复项已更新",
                            "replace": "🔄 完全覆盖：现有数据已被新数据完全替换",
                            "timestamp": f"🕒 时间戳模式：已创建新数据库 {final_db_name}"
                        }

                        strategy_msg = strategy_messages.get(merge_strategy, "")
                        success_message = f"✅ 数据库操作成功！\n📂 数据库名称: {final_db_name}\n{strategy_msg}"

                        # 获取统计信息
                        stats_success, stats_msg, stats = manager.get_database_statistics()
                        if stats_success:
                            stats_html = f"""
                            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                                <h3 style="margin-top: 0; color: #333;">📊 数据库统计信息</h3>
                                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                                    <p style="margin: 4px 0;"><strong>📂 数据库信息:</strong></p>
                                    <p style="margin: 4px 0;">• 数据库名称: {final_db_name}</p>
                                    <p style="margin: 4px 0;">• 数据库类型: {db_type.upper()}</p>
                                    <p style="margin: 4px 0;"><strong>📈 数据统计:</strong></p>
                                    <p style="margin: 4px 0;">• 定额项数量: {stats['quota_count']} 个</p>
                                    <p style="margin: 4px 0;">• 资源记录数: {stats['resource_count']} 个</p>
                                    <p style="margin: 4px 0;">• 关联定额项: {stats['linked_quota_count']} 个</p>
                                    <p style="margin: 4px 0;">• 数据库大小: {stats['db_size_kb']:.2f} KB</p>
                                    <p style="margin: 4px 0;">• 最后更新: {stats['last_updated'] or 'N/A'}</p>
                                </div>
                            </div>
                            """
                        else:
                            stats_html = "<p style='color: orange;'>统计信息获取失败</p>"

                        return success_message, stats_html
                    else:
                        return message, "<p style='color: red;'>数据库创建失败</p>"

                except Exception as e:
                    error_msg = f"❌ 创建数据库失败: {str(e)}"
                    return error_msg, f"<p style='color: red;'>{error_msg}</p>"





            # 简化的刷新模型函数
            def refresh_models():
                """刷新可用模型列表"""
                # 重新加载API密钥
                self.ai_processor.reload_api_keys()

                # 检查模型状态
                status_lines = []

                # 检查千问QVQ-Max
                if self.ai_processor.api_keys["dashscope"]:
                    status_lines.append("✅ 阿里通义千问-QVQ-Max: 已配置")
                else:
                    status_lines.append("⚠️ 阿里通义千问-QVQ-Max: 需要配置API密钥")

                # 检查LM Studio
                if self.ai_processor._check_lm_studio_qwen_available():
                    status_lines.append("✅ LM Studio qwen2.5-vl-7b: 可用")
                else:
                    status_lines.append("⚠️ LM Studio qwen2.5-vl-7b: 不可用")

                status_text = "\n".join(status_lines)

                return status_text

            # 模态框控制函数
            def show_config_modal():
                """显示配置模态框"""
                return gr.update(visible=True)

            def hide_config_modal():
                """隐藏配置模态框"""
                return gr.update(visible=False)

            # Provider选择处理函数
            def on_provider_change(provider):
                """当选择provider时显示对应的配置区域"""
                if not provider:
                    return (
                        gr.update(visible=False),  # dashscope_config
                        gr.update(visible=False),  # deepseek_config
                        gr.update(visible=False),  # openai_config
                        gr.update(visible=False),  # anthropic_config
                        gr.update(visible=False),  # custom_openai_config
                        gr.update(visible=False),  # ollama_config
                        gr.update(visible=False),  # lm_studio_config
                        gr.update(choices=[], value=None),  # provider_models
                        """
                            <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px;">
                                <p style="margin: 0; color: #667eea; text-align: center;">
                                    <span style="font-size: 1.2em;">ℹ️</span> 请选择AI服务提供商开始配置
                                </p>
                            </div>
                        """
                    )

                # 根据provider显示对应的配置区域
                updates = {
                    "dashscope": (True, False, False, False, False, False, False),
                    "deepseek": (False, True, False, False, False, False, False),
                    "openai": (False, False, True, False, False, False, False),
                    "anthropic": (False, False, False, True, False, False, False),
                    "custom_openai": (False, False, False, False, True, False, False),
                    "ollama": (False, False, False, False, False, True, False),
                    "lm_studio": (False, False, False, False, False, False, True)
                }

                visibility = updates.get(provider, (False, False, False, False, False, False, False))

                provider_names = {
                    "dashscope": "阿里云百炼",
                    "deepseek": "DeepSeek",
                    "openai": "OpenAI",
                    "anthropic": "Anthropic",
                    "custom_openai": "自定义OpenAI兼容",
                    "ollama": "本地Ollama",
                    "lm_studio": "LM Studio"
                }

                # 获取该provider下的可用模型
                provider_models = self.ai_processor.get_models_by_provider(provider)
                model_choices = list(provider_models.items()) if provider_models else []

                status_html = f"""
                    <div style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px;">
                        <p style="margin: 0; color: #667eea; text-align: center;">
                            <span style="font-size: 1.2em;">🎯</span> 已选择: {provider_names.get(provider, provider)}
                        </p>
                        <p style="margin: 5px 0 0 0; color: #666; text-align: center; font-size: 0.9em;">
                            请填写配置信息并测试连接
                        </p>
                        {f'<p style="margin: 5px 0 0 0; color: #28a745; text-align: center; font-size: 0.9em;">🎉 检测到 {len(model_choices)} 个可用模型</p>' if model_choices else '<p style="margin: 5px 0 0 0; color: #ffc107; text-align: center; font-size: 0.9em;">⚠️ 请先配置API密钥</p>'}
                    </div>
                """

                return (
                    gr.update(visible=visibility[0]),  # dashscope_config
                    gr.update(visible=visibility[1]),  # deepseek_config
                    gr.update(visible=visibility[2]),  # openai_config
                    gr.update(visible=visibility[3]),  # anthropic_config
                    gr.update(visible=visibility[4]),  # custom_openai_config
                    gr.update(visible=visibility[5]),  # ollama_config
                    gr.update(visible=visibility[6]),  # lm_studio_config
                    gr.update(choices=model_choices, value=model_choices[0][0] if model_choices else None),  # provider_models
                    status_html
                )





            # 刷新模型列表函数
            def refresh_quota_models():
                """刷新定额识别模型列表"""
                try:
                    # 刷新AI模型处理器的模型列表
                    success = self.ai_processor.refresh_lm_studio_models()

                    # 获取可用模型
                    available_models = self.ai_processor.get_available_models()

                    # 转换为选择列表格式
                    choices = [(name, key) for key, name in available_models.items()]

                    # 获取LM Studio状态
                    lm_status = self.ai_processor.get_lm_studio_status()

                    # 生成状态HTML
                    if lm_status["running"]:
                        status_html = f"""
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 12px; margin: 8px 0;">
                            <h4 style="color: #155724; margin: 0 0 8px 0;">✅ LM Studio运行正常</h4>
                            <p style="color: #155724; margin: 0; font-size: 0.9em;">
                                • 总模型数: {lm_status["models_count"]} 个<br>
                                • 视觉模型数: {lm_status["vision_models_count"]} 个<br>
                                • 服务地址: {lm_status.get("base_url", "http://127.0.0.1:1234")}
                            </p>
                        </div>
                        """
                    else:
                        status_html = f"""
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 12px; margin: 8px 0;">
                            <h4 style="color: #721c24; margin: 0 0 8px 0;">❌ LM Studio未运行</h4>
                            <p style="color: #721c24; margin: 0; font-size: 0.9em;">
                                {lm_status["message"]}<br>
                                💡 请启动LM Studio并加载视觉语言模型
                            </p>
                        </div>
                        """

                    # 选择默认值
                    default_value = None
                    if choices:
                        # 优先选择LM Studio模型
                        lm_studio_choices = [choice for choice in choices if choice[1].startswith("lm_studio_")]
                        if lm_studio_choices:
                            default_value = lm_studio_choices[0][1]
                        else:
                            default_value = choices[0][1]

                    return gr.update(choices=choices, value=default_value), status_html

                except Exception as e:
                    error_html = f"""
                    <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 12px; margin: 8px 0;">
                        <h4 style="color: #721c24; margin: 0 0 8px 0;">❌ 刷新模型失败</h4>
                        <p style="color: #721c24; margin: 0; font-size: 0.9em;">
                            错误信息: {str(e)}
                        </p>
                    </div>
                    """
                    return gr.update(choices=[("无可用模型", "")], value=""), error_html

            # 绑定事件
            # PDF上传和浏览器
            pdf_input.change(
                fn=load_pdf,
                inputs=[pdf_input],
                outputs=[pdf_viewer, pdf_controls, current_page, total_pages_display, current_pdf_path]
            )

            # 已存储PDF相关事件
            refresh_stored_btn.click(
                fn=get_stored_quota_pdfs,
                outputs=[stored_pdf_dropdown]
            )

            stored_pdf_dropdown.change(
                fn=show_stored_pdf_info,
                inputs=[stored_pdf_dropdown],
                outputs=[stored_pdf_info]
            )

            stored_pdf_dropdown.change(
                fn=load_stored_pdf,
                inputs=[stored_pdf_dropdown],
                outputs=[pdf_viewer, pdf_controls, current_page, total_pages_display, current_pdf_path]
            )

            # 页面导航
            current_page.change(
                fn=show_page,
                inputs=[current_pdf_path, current_page],
                outputs=[pdf_viewer]
            )

            prev_page_btn.click(
                fn=prev_page,
                inputs=[current_pdf_path, current_page],
                outputs=[current_page, pdf_viewer]
            )

            next_page_btn.click(
                fn=next_page,
                inputs=[current_pdf_path, current_page, total_pages_display],
                outputs=[current_page, pdf_viewer]
            )

            zoom_btn.click(
                fn=zoom_page,
                inputs=[current_pdf_path, current_page],
                outputs=[pdf_viewer]
            )

            process_btn.click(
                fn=process_wrapper,
                inputs=[current_pdf_path, model_dropdown, start_page, end_page, volume_code, chapter_codes],
                outputs=[csv_output, status_text, processing_details, extraction_preview, csv_output],
                show_progress=True
            )

            # 简化的API密钥保存事件
            save_api_key_btn.click(
                fn=save_api_key,
                inputs=[api_key_input],
                outputs=[model_status]
            )

            # 定额识别模型连接测试事件
            test_quota_model_btn.click(
                fn=test_quota_model_connection,
                inputs=[model_dropdown],
                outputs=[quota_model_test_output]
            )

            # 刷新模型列表事件
            refresh_models_btn.click(
                fn=refresh_quota_models,
                outputs=[model_dropdown, lm_studio_status]
            )

            # 删除复杂配置相关的事件绑定


            # 档案管理事件绑定
            refresh_files_btn.click(
                fn=refresh_files_list,
                outputs=[files_list, archive_status]
            )

            files_list.change(
                fn=preview_selected_file,
                inputs=[files_list],
                outputs=[file_preview]
            )

            files_list.change(
                fn=update_merge_preview,
                inputs=[files_list, merge_mode],
                outputs=[file_preview]
            )

            merge_mode.change(
                fn=update_merge_preview,
                inputs=[files_list, merge_mode],
                outputs=[file_preview]
            )

            delete_selected_btn.click(
                fn=delete_selected_files,
                inputs=[files_list],
                outputs=[files_list, archive_status]
            )

            merge_selected_btn.click(
                fn=merge_selected_files,
                inputs=[files_list, merge_filename, merge_mode],
                outputs=[merged_file_output, archive_status, merged_file_output]
            )

            # 数据库转换事件绑定
            convert_selected_btn.click(
                fn=convert_to_database,
                inputs=[files_list, db_format, db_filename],
                outputs=[db_file_output, db_conversion_status, db_file_output, db_preview]
            )

            preview_db_btn.click(
                fn=preview_database_file,
                inputs=[files_list, db_format, db_filename],
                outputs=[db_preview]
            )

            # 当数据库格式改变时，自动更新文件名扩展名
            def update_db_filename(format_type, current_filename):
                """根据数据库格式更新文件名"""
                base_name = current_filename.split('.')[0] if '.' in current_filename else current_filename

                if format_type == "sqlite":
                    return f"{base_name}.db"
                elif format_type == "mongodb":
                    return f"{base_name}.json"
                else:
                    return f"{base_name}.sql"

            db_format.change(
                fn=update_db_filename,
                inputs=[db_format, db_filename],
                outputs=[db_filename]
            )

            # 定额创建工具事件绑定 - 优化为更快的响应
            enterprise_db_type.change(
                fn=update_enterprise_db_config,
                inputs=[enterprise_db_type],
                outputs=[enterprise_db_host, enterprise_db_port, enterprise_default_db, enterprise_db_user, enterprise_db_password, test_db_connection_btn, enterprise_db_test_result, enterprise_db_name],
                show_progress=False  # 禁用进度条以提高响应速度
            )

            refresh_enterprise_files_btn.click(
                fn=refresh_enterprise_files,
                outputs=[parent_quotas_files, child_resources_files]
            )

            test_db_connection_btn.click(
                fn=test_database_connection,
                inputs=[enterprise_db_type, enterprise_db_name, enterprise_db_host, enterprise_db_port, enterprise_default_db, enterprise_db_user, enterprise_db_password],
                outputs=[enterprise_db_test_result]
            )

            create_enterprise_db_btn.click(
                fn=create_enterprise_database,
                inputs=[
                    enterprise_db_type, enterprise_db_name, enterprise_db_host,
                    enterprise_db_port, enterprise_default_db, enterprise_db_user, enterprise_db_password,
                    parent_quotas_files, child_resources_files, merge_strategy
                ],
                outputs=[enterprise_creation_status, enterprise_db_stats]
            )



            # 页面加载时自动刷新文件列表
            interface.load(
                fn=refresh_files_list,
                outputs=[files_list, archive_status]
            )

            # 页面加载时自动刷新企业定额文件列表
            interface.load(
                fn=refresh_enterprise_files,
                outputs=[parent_quotas_files, child_resources_files]
            )

            # 高级定额管理系统事件绑定
            if self.advanced_quota_handlers:
                # 数据库类型变化事件 - 优化响应速度
                advanced_connection_components['db_type_dropdown'].change(
                    fn=self.advanced_quota_handlers.update_db_config_visibility,
                    inputs=[advanced_connection_components['db_type_dropdown']],
                    outputs=[
                        advanced_connection_components['sqlite_config'],
                        advanced_connection_components['other_db_config'],
                        advanced_connection_components['db_port_input']
                    ],
                    show_progress=False  # 禁用进度条以提高响应速度
                )

                # 测试连接事件
                advanced_connection_components['test_connection_btn'].click(
                    fn=self.advanced_quota_handlers.test_database_connection,
                    inputs=[
                        advanced_connection_components['db_type_dropdown'],
                        advanced_connection_components['db_path_input'],
                        advanced_connection_components['db_host_input'],
                        advanced_connection_components['db_port_input'],
                        advanced_connection_components['db_name_input'],
                        advanced_connection_components['db_user_input'],
                        advanced_connection_components['db_password_input']
                    ],
                    outputs=[advanced_connection_components['connection_status']]
                )

                # 连接数据库事件
                advanced_connection_components['connect_db_btn'].click(
                    fn=self.advanced_quota_handlers.connect_to_database,
                    inputs=[
                        advanced_connection_components['db_type_dropdown'],
                        advanced_connection_components['db_path_input'],
                        advanced_connection_components['db_host_input'],
                        advanced_connection_components['db_port_input'],
                        advanced_connection_components['db_name_input'],
                        advanced_connection_components['db_user_input'],
                        advanced_connection_components['db_password_input']
                    ],
                    outputs=[
                        advanced_connection_components['connection_status'],
                        advanced_connection_components['disconnect_db_btn'],
                        advanced_connection_components['connect_db_btn'],
                        advanced_browser_components['table_selector'],
                        advanced_browser_components['db_schema_tree'],
                        advanced_browser_components['table_data_display'],
                        advanced_browser_components['pagination_controls'],
                        advanced_browser_components['data_operation_btns']
                    ]
                )

                # 断开连接事件
                advanced_connection_components['disconnect_db_btn'].click(
                    fn=self.advanced_quota_handlers.disconnect_from_database,
                    outputs=[
                        advanced_connection_components['connection_status'],
                        advanced_connection_components['disconnect_db_btn'],
                        advanced_connection_components['connect_db_btn'],
                        advanced_browser_components['table_selector'],
                        advanced_browser_components['db_schema_tree'],
                        advanced_browser_components['table_data_display'],
                        advanced_browser_components['pagination_controls'],
                        advanced_browser_components['data_operation_btns']
                    ]
                )

                # 表选择事件
                advanced_browser_components['table_selector'].change(
                    fn=self.advanced_quota_handlers.load_table_data,
                    inputs=[advanced_browser_components['table_selector']],
                    outputs=[
                        advanced_browser_components['table_data_display'],
                        advanced_browser_components['pagination_controls'],
                        advanced_browser_components['data_operation_btns'],
                        advanced_browser_components['page_info']
                    ]
                )

                # 分页按钮事件
                advanced_browser_components['next_page_btn'].click(
                    fn=self.advanced_quota_handlers.next_page,
                    outputs=[
                        advanced_browser_components['table_data_display'],
                        advanced_browser_components['page_info']
                    ]
                )

                advanced_browser_components['prev_page_btn'].click(
                    fn=self.advanced_quota_handlers.prev_page,
                    outputs=[
                        advanced_browser_components['table_data_display'],
                        advanced_browser_components['page_info']
                    ]
                )

                # 搜索定额事件
                advanced_search_components['search_btn'].click(
                    fn=self.advanced_quota_handlers.search_quotas,
                    inputs=[advanced_search_components['search_input']],
                    outputs=[
                        advanced_search_components['quota_search_results'],
                        advanced_search_components['selected_quota_info'],
                        advanced_search_components['related_resources_display']
                    ]
                )

                # 加载资源列表事件
                advanced_price_components['load_resources_btn'].click(
                    fn=self.advanced_quota_handlers.load_unique_resources,
                    outputs=[
                        advanced_price_components['unique_resources_display'],
                        advanced_price_components['price_update_status']
                    ]
                )

                # 重新计算定额价格事件
                advanced_price_components['recalculate_btn'].click(
                    fn=self.advanced_quota_handlers.recalculate_quota_prices,
                    outputs=[advanced_price_components['price_update_status']]
                )

                # 资源选择事件
                advanced_price_components['unique_resources_display'].select(
                    fn=self.advanced_quota_handlers.select_resource_for_price_update,
                    inputs=[advanced_price_components['unique_resources_display']],
                    outputs=[
                        advanced_price_components['selected_resource_info'],
                        advanced_price_components['new_price_input'],
                        advanced_price_components['selected_resource_code']
                    ]
                )

                # 更新选中资源价格事件
                advanced_price_components['update_price_btn'].click(
                    fn=self.advanced_quota_handlers.update_selected_resource_price,
                    inputs=[
                        advanced_price_components['selected_resource_code'],
                        advanced_price_components['new_price_input']
                    ],
                    outputs=[advanced_price_components['price_update_status']]
                )

                # 信息价载入相关事件
                advanced_price_components['scan_price_db_btn'].click(
                    fn=self.advanced_quota_handlers.scan_price_info_databases,
                    outputs=[advanced_price_components['price_info_databases']]
                )

                advanced_price_components['load_price_info_btn'].click(
                    fn=self.advanced_quota_handlers.load_price_info_data,
                    inputs=[advanced_price_components['price_info_databases']],
                    outputs=[
                        advanced_price_components['price_info_load_status'],
                        advanced_price_components['price_match_stats']
                    ]
                )

            # 程序运行日志区域
            gr.HTML("<hr class='divider'>")

            with gr.Group(elem_classes="log-section fade-in-up"):
                gr.HTML("""
                    <h2 style="color: #667eea; text-align: center; margin-bottom: 20px;">
                        <span class="icon">📋</span>程序运行日志
                    </h2>
                    <p style="text-align: center; color: #666; margin-bottom: 20px;">
                        实时显示系统运行状态和详细处理过程，便于开发期问题排查
                    </p>
                """)

                with gr.Row():
                    with gr.Column(scale=2):
                        log_level_filter = gr.Dropdown(
                            label="🔍 日志级别过滤",
                            choices=[
                                ("全部", "ALL"),
                                ("信息", "INFO"),
                                ("警告", "WARNING"),
                                ("错误", "ERROR"),
                                ("调试", "DEBUG")
                            ],
                            value="ALL",
                            interactive=True
                        )

                    with gr.Column(scale=2):
                        log_module_filter = gr.Dropdown(
                            label="📦 模块过滤",
                            choices=[
                                ("全部模块", "ALL"),
                                ("主应用", "MainApp"),
                                ("PDF处理", "PDFProcessor"),
                                ("AI模型", "AIModelProcessor"),
                                ("数据处理", "DataProcessor"),
                                ("数据库转换", "MCPDatabaseConverter"),
                                ("高级定额管理", "AdvancedQuotaManager"),
                                ("信息价识别", "IntelligentPriceInfo")
                            ],
                            value="ALL",
                            interactive=True
                        )

                    with gr.Column(scale=1):
                        refresh_logs_btn = gr.Button(
                            "🔄 刷新日志",
                            variant="secondary",
                            size="sm"
                        )

                    with gr.Column(scale=1):
                        clear_logs_btn = gr.Button(
                            "🗑️ 清空日志",
                            variant="secondary",
                            size="sm"
                        )

                    with gr.Column(scale=1):
                        export_logs_btn = gr.Button(
                            "📤 导出日志",
                            variant="secondary",
                            size="sm"
                        )

                # 日志显示区域
                log_display = gr.HTML(
                    label="运行日志",
                    value="<p style='color: #666; text-align: center; padding: 20px;'>系统启动中，日志加载中...</p>",
                    elem_classes="log-display"
                )

                # 日志统计信息
                log_stats = gr.HTML(
                    label="日志统计",
                    value="",
                    elem_classes="log-stats"
                )

                # 日志相关事件绑定
                refresh_logs_btn.click(
                    fn=self.refresh_logs,
                    inputs=[log_level_filter, log_module_filter],
                    outputs=[log_display, log_stats]
                )

                clear_logs_btn.click(
                    fn=self.clear_logs,
                    outputs=[log_display, log_stats]
                )

                export_logs_btn.click(
                    fn=self.export_logs,
                    outputs=[log_stats]
                )

                # 过滤器变化时自动刷新
                log_level_filter.change(
                    fn=self.refresh_logs,
                    inputs=[log_level_filter, log_module_filter],
                    outputs=[log_display, log_stats]
                )

                log_module_filter.change(
                    fn=self.refresh_logs,
                    inputs=[log_level_filter, log_module_filter],
                    outputs=[log_display, log_stats]
                )

            # 页面加载时自动刷新模型列表和已存储PDF列表
            interface.load(
                fn=refresh_quota_models,
                outputs=[model_dropdown, lm_studio_status]
            )

            interface.load(
                fn=get_stored_quota_pdfs,
                outputs=[stored_pdf_dropdown]
            )

        return interface



    def _save_api_key(self, env_key: str, api_key: str) -> bool:
        """保存API密钥到.env文件"""
        try:
            import os
            from dotenv import load_dotenv, set_key

            env_file = ".env"

            # 确保.env文件存在
            if not os.path.exists(env_file):
                with open(env_file, 'w') as f:
                    f.write("")

            # 设置环境变量
            set_key(env_file, env_key, api_key)

            # 同时设置到当前环境
            os.environ[env_key] = api_key

            return True
        except Exception as e:
            print(f"保存API密钥失败: {e}")
            return False

    def _get_model_status_text(self) -> str:
        """获取简化的模型状态文本"""
        status_lines = []

        # 检查千问QVQ-Max
        import os
        if os.environ.get("DASHSCOPE_API_KEY"):
            status_lines.append("✅ 阿里通义千问-QVQ-Max: 已配置")
        else:
            status_lines.append("⚠️ 阿里通义千问-QVQ-Max: 需要配置API密钥")

        # 检查LM Studio qwen2.5-vl-7b
        if self.ai_processor._check_lm_studio_qwen_available():
            status_lines.append("✅ LM Studio qwen2.5-vl-7b: 可用")
        else:
            status_lines.append("⚠️ LM Studio qwen2.5-vl-7b: 不可用")
            status_lines.append("")
            status_lines.append("💡 启动LM Studio:")
            status_lines.append("1. 启动LM Studio应用")
            status_lines.append("2. 加载qwen2.5-vl-7b模型")
            status_lines.append("3. 确保服务运行在 http://127.0.0.1:1234")

        return "\n".join(status_lines)

    def generate_database_name_with_timestamp(self, base_name: str, add_timestamp: bool = True) -> str:
        """生成带时间戳的数据库名称"""
        if not add_timestamp:
            return base_name

        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 处理文件扩展名
        if '.' in base_name:
            name_parts = base_name.rsplit('.', 1)
            return f"{name_parts[0]}_{timestamp}.{name_parts[1]}"
        else:
            return f"{base_name}_{timestamp}"

    def check_database_exists(self, db_type: str, db_config: dict) -> tuple[bool, str]:
        """检查数据库是否存在"""
        try:
            if db_type == "sqlite":
                db_path = db_config.get('database_path', '')
                exists = os.path.exists(db_path)
                return exists, f"SQLite文件: {db_path}"

            elif db_type == "mongodb":
                json_path = db_config.get('database_path', '')
                exists = os.path.exists(json_path)
                return exists, f"MongoDB JSON文件: {json_path}"

            elif db_type in ["mysql", "postgresql"]:
                # 对于MySQL和PostgreSQL，我们假设数据库可能存在
                # 实际检查需要连接数据库，这里简化处理
                db_name = db_config.get('database', '')
                return True, f"{db_type.upper()}数据库: {db_name} (可能存在)"

            return False, "未知数据库类型"

        except Exception as e:
            return False, f"检查失败: {str(e)}"

    def refresh_logs(self, level_filter: str, module_filter: str) -> tuple[str, str]:
        """刷新日志显示"""
        try:
            # 获取日志记录
            if level_filter == "ALL":
                if module_filter == "ALL":
                    logs = self.log_manager.get_recent_logs(100)
                else:
                    logs = self.log_manager.get_logs_by_module(module_filter, 100)
            else:
                logs = self.log_manager.get_logs_by_level(level_filter, 100)
                if module_filter != "ALL":
                    logs = [log for log in logs if module_filter.lower() in log['module'].lower()]

            # 格式化日志显示
            if logs:
                log_html = self._format_logs_for_display(logs)

                # 生成统计信息
                total_logs = len(self.log_manager.log_history)
                filtered_logs = len(logs)

                # 按级别统计
                level_counts = {}
                for log in self.log_manager.log_history:
                    level = log['level']
                    level_counts[level] = level_counts.get(level, 0) + 1

                stats_html = f"""
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;">
                    <strong>📊 日志统计:</strong>
                    总计 {total_logs} 条 | 当前显示 {filtered_logs} 条<br>
                    <span style="color: #17a2b8;">INFO: {level_counts.get('INFO', 0)}</span> |
                    <span style="color: #ffc107;">WARNING: {level_counts.get('WARNING', 0)}</span> |
                    <span style="color: #dc3545;">ERROR: {level_counts.get('ERROR', 0)}</span> |
                    <span style="color: #6c757d;">DEBUG: {level_counts.get('DEBUG', 0)}</span>
                </div>
                """
            else:
                log_html = "<p style='color: #666; text-align: center; padding: 20px;'>暂无符合条件的日志记录</p>"
                stats_html = "<div style='color: #666;'>暂无统计信息</div>"

            return log_html, stats_html

        except Exception as e:
            error_msg = f"刷新日志失败: {str(e)}"
            self.logger.error(error_msg)
            return f"<p style='color: red;'>{error_msg}</p>", ""

    def clear_logs(self) -> tuple[str, str]:
        """清空日志"""
        try:
            self.log_manager.clear_logs()
            return "<p style='color: #666; text-align: center; padding: 20px;'>日志已清空</p>", ""
        except Exception as e:
            error_msg = f"清空日志失败: {str(e)}"
            return f"<p style='color: red;'>{error_msg}</p>", ""

    def export_logs(self) -> str:
        """导出日志"""
        try:
            export_path = self.log_manager.export_logs()
            return f"<p style='color: green;'>✅ 日志已导出到: {export_path}</p>"
        except Exception as e:
            error_msg = f"导出日志失败: {str(e)}"
            self.logger.error(error_msg)
            return f"<p style='color: red;'>{error_msg}</p>"

    def _format_logs_for_display(self, logs: list) -> str:
        """格式化日志用于界面显示"""
        if not logs:
            return "<p style='color: #666; text-align: center; padding: 20px;'>暂无日志记录</p>"

        formatted_lines = []
        for log in logs[-50:]:  # 只显示最近50条
            level_color = self._get_level_color(log['level'])
            level_icon = self._get_level_icon(log['level'])

            line = f"""
            <div style="margin-bottom: 5px; padding: 5px; border-left: 3px solid {level_color}; background: #f8f9fa;">
                <span style="color: #666; font-size: 0.9em;">{log['timestamp']}</span>
                <span style="color: {level_color}; font-weight: bold; margin: 0 10px;">{level_icon} {log['level']}</span>
                <span style="color: #495057; font-weight: 500;">[{log['module']}]</span>
                <br>
                <span style="color: #212529; margin-left: 10px;">{log['message']}</span>
            </div>
            """
            formatted_lines.append(line)

        return "".join(formatted_lines)

    def _get_level_color(self, level: str) -> str:
        """获取日志级别对应的颜色"""
        colors = {
            'DEBUG': '#6c757d',
            'INFO': '#17a2b8',
            'WARNING': '#ffc107',
            'ERROR': '#dc3545',
            'CRITICAL': '#6f42c1'
        }
        return colors.get(level, '#333')

    def _get_level_icon(self, level: str) -> str:
        """获取日志级别对应的图标"""
        icons = {
            'DEBUG': '🔍',
            'INFO': 'ℹ️',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'CRITICAL': '🚨'
        }
        return icons.get(level, 'ℹ️')



def print_startup_info():
    """打印启动信息"""
    from datetime import datetime
    import socket
    import platform
    import sys

    # 获取本机IP地址
    def get_local_ip():
        try:
            # 连接到一个远程地址来获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"

    local_ip = get_local_ip()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    print("\n" + "="*80)
    print("🔧 北京市2021消耗定额创建工具 | Beijing Quota Creation Tool")
    print("="*80)
    print(f"🕒 启动时间: {current_time}")
    print(f"🖥️  系统信息: {platform.system()} {platform.release()}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📂 工作目录: {os.getcwd()}")
    print("-"*80)
    print("🌐 服务器信息:")
    print(f"   • 本地访问: http://localhost:7864")
    print(f"   • 局域网访问: http://{local_ip}:7864")
    print(f"   • 服务器端口: 7864")
    print(f"   • 绑定地址: 0.0.0.0 (所有网络接口)")
    print("-"*80)
    print("📋 功能模块:")
    print("   • 🤖 AI定额识别 - 智能识别PDF中的定额数据")
    print("   • 🗄️ 数据库创建 - 支持SQLite、MongoDB、MySQL、PostgreSQL")
    print("   • 📊 信息价识别 - 识别信息价数据并写入数据库")
    print("   • ⚡ 高级定额管理 - 完整的数据库管理和查询功能")
    print("   • 📋 程序运行日志 - 实时显示系统运行状态")
    print("-"*80)
    print("💡 使用提示:")
    print("   • 首次使用请先配置AI模型API密钥")
    print("   • 支持多种数据库类型，推荐使用SQLite开始")
    print("   • 所有输出文件保存在 output/ 目录下")
    print("   • 程序日志保存在 logs/ 目录下")
    print("="*80)
    print("🚀 正在启动Web服务器...")
    print()

def print_startup_success():
    """打印启动成功信息"""
    from datetime import datetime
    import socket

    def get_local_ip():
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"

    local_ip = get_local_ip()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    print("\n" + "="*80)
    print("✅ 服务器启动成功！")
    print("="*80)
    print(f"🕒 启动完成时间: {current_time}")
    print(f"🌐 访问地址:")
    print(f"   • 本地访问: http://localhost:7864")
    print(f"   • 局域网访问: http://{local_ip}:7864")
    print("-"*80)
    print("📱 快速访问:")
    print("   • 定额识别: 主页面 → AI定额识别")
    print("   • 数据库创建: 主页面 → 定额创建工具")
    print("   • 信息价识别: 高级定额管理系统 → 信息价识别")
    print("   • 数据库管理: 高级定额管理系统 → 数据库浏览器")
    print("-"*80)
    print("💡 提示: 按 Ctrl+C 停止服务器")
    print("="*80)
    print()

def main():
    """主函数"""
    app = QuotaExtractionApp()
    interface = app.create_interface()
    
    # 确保输出目录存在
    os.makedirs("output", exist_ok=True)
    os.makedirs("output/price_info", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    os.makedirs("static/fonts", exist_ok=True)

    # 显示启动信息
    print_startup_info()

    # 启动界面
    try:
        interface.launch(
            server_name="0.0.0.0",
            server_port=7864,
            share=False,
            debug=False,  # 关闭调试模式减少错误信息
            show_error=False,  # 不显示错误页面
            quiet=False,  # 显示启动信息
            allowed_paths=["output", "output/price_info", "static"],  # 允许访问输出目录和静态文件
            favicon_path="static/favicon.ico"  # 设置favicon路径
        )
    except KeyboardInterrupt:
        print("\n" + "="*80)
        print("⏹️  服务器已停止")
        print("👋 感谢使用北京市2021消耗定额创建工具！")
        print("="*80)
    except Exception as e:
        print("\n" + "="*80)
        print(f"❌ 服务器启动失败: {str(e)}")
        print("💡 请检查端口7864是否被占用，或尝试重新启动")
        print("="*80)

if __name__ == "__main__":
    main()
